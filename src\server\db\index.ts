/**
 * 通用数据库连接抽象层
 * 提供数据库连接的基础设施，不包含具体业务schema
 *
 * 注意：具体的业务数据库配置应该在 src/app/db/ 中
 */

import { createClient } from '@libsql/client';
import { drizzle } from 'drizzle-orm/libsql';

// 通用数据库配置接口
export interface DatabaseConfig {
  url: string;
  authToken?: string;
}

// 默认数据库配置
export const defaultDbConfig: DatabaseConfig = {
  url: process.env.DATABASE_URL || 'file:./data/default.db',
  authToken: process.env.DATABASE_AUTH_TOKEN,
};

/**
 * 创建数据库连接的工厂函数
 * @param config 数据库配置
 * @param schema 可选的schema对象
 * @returns 数据库实例和原始客户端
 */
export function createDatabaseConnection(config: DatabaseConfig = defaultDbConfig, schema?: any) {
  const client = createClient(config);
  const db = drizzle(client, schema ? { schema } : { schema: {} });

  return {
    db,
    rawClient: client,
    client // 别名，向后兼容
  };
}

// 默认连接实例（用于向后兼容）
const { db, rawClient, client } = createDatabaseConnection();

export { db, rawClient, client };

// 导出类型
export type DatabaseInstance = typeof db;
