import { SQL_OPERATORS } from "../constants";
import { sql, type SQL } from "drizzle-orm";
import type { QueryParams, FilterableField } from "../types";
import { z } from "zod";
import { FILTER_OPERATORS } from "../types";
export class QueryUtils {
  // 系统保留的查询参数
  private static readonly RESERVED_PARAMS = new Set([
    "search",
    "sort",
    "order",
    "page",
    "limit",
    "include", // 用于关联查询
    "fields", // 用于字段选择
    "group", // 用于分组
    "distinct", // 用于去重
  ]);

  /**
   * Parse sort fields string into array of sort configs
   * @example
   * parseSortFields("createdAt:desc,name:asc") // [{ field: "createdAt", order: "desc" }, { field: "name", order: "asc" }]
   */
  static parseSortFields(
    sortStr?: string
  ): Array<{ field: string; order: "asc" | "desc" }> {
    if (!sortStr) return [];

    return sortStr.split(",").map((field) => {
      const [name, order = "asc"] = field.trim().split(":");
      return {
        field: name,
        order: (["asc", "desc"].includes(order.toLowerCase())
          ? order.toLowerCase()
          : "desc") as "asc" | "desc",
      };
    });
  }

  static validateFilterableParams(
    params: Record<string, string>,
    allowedFields: string[],
    options: {
      excludeId?: boolean;
      checkReserved?: boolean;
    } = {}
  ) {
    const invalidParams = Object.keys(params).filter((param) => {
      if (options.excludeId && param === "id") return false;
      if (options.checkReserved && this.RESERVED_PARAMS.has(param))
        return false;
      return !allowedFields.includes(param);
    });

    if (invalidParams.length > 0) {
      throw new Error(
        `Invalid parameters: ${invalidParams.join(", ")}. ` +
        `Allowed parameters: ${allowedFields.join(", ")}`
      );
    }
  }

  static parseQueryParams(
    searchParams: URLSearchParams,
    filterableFields: Record<string, FilterableField>,
    routeParams: string[] = [] // Add route params to exclude from validation
  ): QueryParams {
    const filter: Record<string, any> = {};
    const filterableFieldsKeys = Object.keys(filterableFields);

    // Convert searchParams to object and exclude route params
    const queryParams = Object.fromEntries(
      Array.from(searchParams.entries()).filter(
        ([key]) => !routeParams.includes(key)
      )
    );

    // Validate parameters (excluding route params)
    this.validateFilterableParams(queryParams, filterableFieldsKeys, {
      checkReserved: true,
    });

    // Parse valid filter parameters (excluding route params)
    Array.from(searchParams.entries()).forEach(([key, value]) => {
      if (
        this.RESERVED_PARAMS.has(key) ||
        routeParams.includes(key) ||
        !filterableFieldsKeys.includes(key)
      )
        return;
      filter[key] = this.parseFilterValue(value, filterableFields[key].schema!);
    });
    return {
      search: searchParams.get("search") || undefined,
      filter,
      sort: searchParams.get("sort") || undefined,
      order: (searchParams.get("order") as "asc" | "desc") || undefined,
      page: searchParams.get("page")
        ? parseInt(searchParams.get("page")!)
        : undefined,
      limit: searchParams.get("limit")
        ? parseInt(searchParams.get("limit")!)
        : undefined,
    };
  }

  /**
   * 解析过滤值为标准格式
   * @param value 过滤值字符串
   * @returns 解析后的过滤条件对象
   * @example
   * "active" -> { eq: "active" }
   * "gt:18" -> { gt: 18 }
   * "in:a,b,c" -> { in: ["a", "b", "c"] }
   */
  static parseFilterValue(
    value: string,
    schema: z.ZodType<any>
  ): Record<string, any> {
    // Check if it's an operator format (e.g. "gt:18")
    const operatorMatch = value.match(/^([^:]+):(.+)$/);

    // 先尝试用完整的值验证schema
    const fullValueValid = new RegExp(`^(${FILTER_OPERATORS.join("|")}):`).test(
      value
    )
      ? false
      : schema.safeParse(value).success;
    if (fullValueValid) {
      return { eq: value };
    }

    if (operatorMatch) {
      const [, operator, operatorValue] = operatorMatch;
      // 对于 in 操作符，分割并验证每个值
      if (operator === "in") {
        const values = operatorValue.split(",");
        return { [operator]: values };
      } else if (operator === "like") {
        return { [operator]: `%${operatorValue}%` };
      }
      // 验证操作符值是否符合schema
      const parsedValue = schema.safeParse(operatorValue).success
        ? operatorValue
        : this.parseBasicValue(operatorValue);
      return { [operator]: parsedValue };
    }

    // 如果没有操作符，直接作为相等条件
    return { eq: value };
  }

  private static parseBasicValue(value: string): any {
    // 如果是数字格式的字符串
    if (/^-?\d*\.?\d+$/.test(value)) {
      return Number(value);
    }
    // 如果是布尔值字符串
    if (value === "true" || value === "false") {
      return value === "true";
    }
    // 其他情况保持字符串
    return value;
  }

  static buildSearchCondition(fields: string[], searchTerm: string): SQL {
    const searchConditions = fields
      .map((field) => `${field} ${searchTerm.startsWith("-") ? SQL_OPERATORS.ILIKE : SQL_OPERATORS.LIKE} '%${searchTerm.replace("-", "")}%'`)
      .join(" OR ");
    return sql`(${sql.raw(searchConditions)})`;
  }

  static objectToSql(obj: Record<string, any>): SQL {
    return sql`(${sql.raw(
      Object.entries(obj)
        .map(([key, value]) => `${key} ${SQL_OPERATORS.EQUALS} ${value}`)
        .join(" AND ")
    )})`;
  }

  static buildFilterConditions(
    filterableFields: string[],
    filter: Record<string, Record<string, any>>
  ): Record<string, any> {
    const conditions: Record<string, any> = {};
    for (const [key, value] of Object.entries(filter)) {
      if (filterableFields.includes(key)) {
        conditions[key] = value;
      }
    }
    return conditions;
  }

  /**
   * 统一解析所有查询参数
   */
  static parseAllQueryParams(
    searchParams: URLSearchParams,
    filterableFields: Record<string, FilterableField>,
    routeParams: string[] = []
  ) {
    // 1. 解析基础查询参数
    const baseParams = this.parseQueryParams(
      searchParams,
      filterableFields,
      routeParams
    );

    // 2. 解析排序参数
    const sortFields = this.parseSortFields(
      searchParams.get("sort") || undefined
    );

    // 3. 解析分页参数
    const page = searchParams.get("page")
      ? parseInt(searchParams.get("page")!)
      : undefined;
    const limit = searchParams.get("limit")
      ? parseInt(searchParams.get("limit")!)
      : undefined;

    return {
      ...baseParams,
      sortFields,
      pagination: {
        page,
        limit,
      },
    };
  }
}
