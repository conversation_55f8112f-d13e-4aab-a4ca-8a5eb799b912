import type { Middleware, MiddlewareContext, NextFunction } from "../types";
import { checkAuth } from "./middleware";
import { ResponseUtils } from "../utils/response";
import type { AuthConfig } from "./types";

/**
 * Authentication middleware for API route builders
 * Integrates with the existing middleware system
 */
export class AuthenticationMiddleware {
  constructor(private config?: AuthConfig) {}

  // Return a middleware function
  getMiddleware(): Middleware {
    return async (context: MiddlewareContext, next: NextFunction): Promise<void> => {
      return this.execute(context, next);
    };
  }

  async execute(context: MiddlewareContext, next: NextFunction): Promise<void> {
    try {
      // Extract necessary information from context
      const method = context.method || 'GET';
      const path = context.path || '/';
      const headers = context.headers || new Headers();
      const resourceName = context.state.get('resourceName');
      const params = context.params || {};

      // Perform authentication check
      const authResult = await checkAuth({
        method: method as any,
        path,
        headers,
        resourceName,
        config: this.config,
        params
      });

      if (!authResult.authorized) {
        // Authentication failed - set error response and stop middleware chain
        const reason = authResult.reason || 'Unauthorized';
        
        if (this.config?.onUnauthorized) {
          context.response = this.config.onUnauthorized(reason);
        } else if (reason === 'Authentication required') {
          context.response = ResponseUtils.unauthorized(reason);
        } else {
          if (this.config?.onForbidden) {
            context.response = this.config.onForbidden(reason);
          } else {
            context.response = ResponseUtils.forbidden(reason);
          }
        }
        
        // Don't call next() - stop the middleware chain
        return;
      }

      // Authentication successful - inject auth data into context
      if (this.config?.injectUser !== false && authResult.user) {
        context.state.set('auth', {
          user: authResult.user,
          session: authResult.session
        });
        context.state.set('user', authResult.user);
        context.state.set('session', authResult.session);
      }

      // Continue to next middleware
      await next();

    } catch (error) {
      // Handle unexpected errors
      console.error('Authentication middleware error:', error);
      context.response = ResponseUtils.internalError('Authentication error');
      // Don't call next() on error
    }
  }
}
