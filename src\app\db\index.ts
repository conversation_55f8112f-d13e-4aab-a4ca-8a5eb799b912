import { drizzle } from 'drizzle-orm/libsql';
import { createClient } from '@libsql/client';
import * as schema from '../schemas';

// 数据库连接配置
const client = createClient({
  url: process.env.TURSO_DATABASE_URL || 'file:./data/app.db',
  authToken: process.env.TURSO_AUTH_TOKEN,
});

// 创建数据库实例，包含所有业务schema
export const db = drizzle(client, { schema });

// 原始客户端（用于执行原始SQL）
export const rawClient = client;

// 导出所有schema
export * from '../schemas';

// 导出类型
export type DatabaseInstance = typeof db;
