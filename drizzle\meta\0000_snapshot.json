{"version": "6", "dialect": "sqlite", "id": "dd2c64ba-d26c-48a8-979e-2049e7afb86f", "prevId": "********-0000-0000-0000-************", "tables": {"account": {"name": "account", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "accountId": {"name": "accountId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "providerId": {"name": "providerId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "accessToken": {"name": "accessToken", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "refreshToken": {"name": "refreshToken", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "idToken": {"name": "idToken", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "accessTokenExpiresAt": {"name": "accessTokenExpiresAt", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "refreshTokenExpiresAt": {"name": "refreshTokenExpiresAt", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updatedAt": {"name": "updatedAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"account_userId_user_id_fk": {"name": "account_userId_user_id_fk", "tableFrom": "account", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "api_keys": {"name": "api_keys", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "key": {"name": "key", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"api_keys_key_unique": {"name": "api_keys_key_unique", "columns": ["key"], "isUnique": true}}, "foreignKeys": {"api_keys_user_id_user_id_fk": {"name": "api_keys_user_id_user_id_fk", "tableFrom": "api_keys", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "session": {"name": "session", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "expiresAt": {"name": "expiresAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updatedAt": {"name": "updatedAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "ipAddress": {"name": "ip<PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "userAgent": {"name": "userAgent", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"session_token_unique": {"name": "session_token_unique", "columns": ["token"], "isUnique": true}}, "foreignKeys": {"session_userId_user_id_fk": {"name": "session_userId_user_id_fk", "tableFrom": "session", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "user": {"name": "user", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "emailVerified": {"name": "emailVerified", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updatedAt": {"name": "updatedAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"user_email_unique": {"name": "user_email_unique", "columns": ["email"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "user_usage": {"name": "user_usage", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "credits": {"name": "credits", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "used_credits": {"name": "used_credits", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "reset_at": {"name": "reset_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"user_usage_user_id_user_id_fk": {"name": "user_usage_user_id_user_id_fk", "tableFrom": "user_usage", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "verification": {"name": "verification", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "expiresAt": {"name": "expiresAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updatedAt": {"name": "updatedAt", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "keys": {"name": "keys", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "service_id": {"name": "service_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "key_value": {"name": "key_value", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'active'"}, "usage_count": {"name": "usage_count", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "max_usage": {"name": "max_usage", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "priority": {"name": "priority", "type": "real", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 1}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "last_used_at": {"name": "last_used_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"idx_keys_service_status_usage": {"name": "idx_keys_service_status_usage", "columns": ["service_id", "status", "usage_count"], "isUnique": false}, "idx_keys_last_used": {"name": "idx_keys_last_used", "columns": ["last_used_at"], "isUnique": false}}, "foreignKeys": {"keys_service_id_services_id_fk": {"name": "keys_service_id_services_id_fk", "tableFrom": "keys", "tableTo": "services", "columnsFrom": ["service_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "services": {"name": "services", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "config": {"name": "config", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"services_name_unique": {"name": "services_name_unique", "columns": ["name"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "usage_logs": {"name": "usage_logs", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "key_id": {"name": "key_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "service_id": {"name": "service_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "timestamp": {"name": "timestamp", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"idx_usage_logs_service_time": {"name": "idx_usage_logs_service_time", "columns": ["service_id", "timestamp"], "isUnique": false}}, "foreignKeys": {"usage_logs_key_id_keys_id_fk": {"name": "usage_logs_key_id_keys_id_fk", "tableFrom": "usage_logs", "tableTo": "keys", "columnsFrom": ["key_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "usage_logs_service_id_services_id_fk": {"name": "usage_logs_service_id_services_id_fk", "tableFrom": "usage_logs", "tableTo": "services", "columnsFrom": ["service_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}