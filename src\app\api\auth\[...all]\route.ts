/**
 * 认证 API - Catch-all 路由
 * 路径: /api/auth/*
 * 支持的方法: GET, POST
 * 
 * 类似 Next.js 的 [...all] 动态路由，处理所有认证相关的请求
 * 包括: /api/auth/login, /api/auth/logout, /api/auth/session 等
 */

import type { Context } from 'hono';
import { auth } from '@/server/auth';

/**
 * GET /api/auth/* - 处理所有 GET 认证请求
 * 例如: /api/auth/session, /api/auth/user 等
 */
export async function GET(c: Context) {
  try {
    // 使用 Better Auth 的处理器处理请求
    const response = await auth.handler(c.req.raw);
    return response;
  } catch (error) {
    console.error('Auth GET handler error:', error);
    return c.json({ 
      error: 'Authentication error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, 500);
  }
}

/**
 * POST /api/auth/* - 处理所有 POST 认证请求
 * 例如: /api/auth/login, /api/auth/logout, /api/auth/register 等
 */
export async function POST(c: Context) {
  console.log('Auth POST request received');
  console.log('Request URL:', c.req.url);
  console.log('Request method:', c.req.method);
  console.log('Request path:', c.req.path);

  try {
    // 使用 Better Auth 的处理器处理请求
    const response = await auth.handler(c.req.raw);
    console.log('Better Auth response status:', response.status);
    return response;
  } catch (error) {
    console.error('Auth POST handler error:', error);
    return c.json({
      error: 'Authentication error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, 500);
  }
}
