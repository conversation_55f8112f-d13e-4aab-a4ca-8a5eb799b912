import { readFileSync } from 'fs';
import { join } from 'path';
import { rawClient } from './index';

/**
 * 应用层数据库迁移工具
 * 包含所有业务相关的迁移
 */
export async function runAppMigrations() {
  try {
    console.log('🚀 Starting application database migrations...');
    
    // 读取迁移文件
    const migrationPath = join(process.cwd(), 'src/server/db/migrations/001-key-rotation.sql');
    const migrationSQL = readFileSync(migrationPath, 'utf-8');
    
    // 分割 SQL 语句（按分号分割）
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0);
    
    // 执行每个 SQL 语句
    for (const statement of statements) {
      if (statement.trim()) {
        await rawClient.execute(statement);
        console.log(`✅ Executed: ${statement.substring(0, 50)}...`);
      }
    }
    
    console.log('✅ Application database migrations completed successfully!');
    
    // 验证表是否创建成功
    const tables = await rawClient.execute(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name IN ('users', 'api_keys', 'user_usage', 'services', 'keys', 'usage_logs')
    `);
    
    console.log(`📊 Created tables: ${tables.rows.map(row => row.name).join(', ')}`);
    
  } catch (error) {
    console.error('❌ Application migration failed:', error);
    throw error;
  }
}

/**
 * 检查应用数据库表是否存在
 */
export async function checkAppTables() {
  try {
    const result = await rawClient.execute(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name IN ('users', 'api_keys', 'user_usage', 'services', 'keys', 'usage_logs')
    `);
    
    const existingTables = result.rows.map(row => row.name);
    const requiredTables = ['users', 'api_keys', 'user_usage', 'services', 'keys', 'usage_logs'];
    const missingTables = requiredTables.filter(table => !existingTables.includes(table));
    
    return {
      allTablesExist: missingTables.length === 0,
      existingTables,
      missingTables
    };
  } catch (error) {
    console.error('Error checking application tables:', error);
    return {
      allTablesExist: false,
      existingTables: [],
      missingTables: ['users', 'api_keys', 'user_usage', 'services', 'keys', 'usage_logs']
    };
  }
}

/**
 * 自动运行应用迁移（如果需要）
 */
export async function autoMigrateApp() {
  const { allTablesExist, missingTables } = await checkAppTables();
  
  if (!allTablesExist) {
    console.log(`🔧 Missing application tables detected: ${missingTables.join(', ')}`);
    console.log('🚀 Running automatic application migration...');
    await runAppMigrations();
  } else {
    console.log('✅ All required application tables exist, skipping migration');
  }
}
