import type {
  Middleware,
  AuthMiddlewareOptions,
  HttpMethod,
  MethodAuthConfig,
  AuthValidatorFn,
} from "../../server/types";
import { auth } from "@/server/auth";
import { ResponseUtils } from "../../server/utils/response";
import { db } from "@/app/db";
import { users } from "../schemas/auth";
import { eq } from "drizzle-orm";

function isValidatorFn(value: MethodAuthConfig): value is AuthValidatorFn {
  return typeof value === "function";
}

function isRolesArray(value: MethodAuthConfig): value is string[] {
  return Array.isArray(value);
}

export function createAuthMiddleware(
  options: AuthMiddlewareOptions = {}
): Middleware {
  const { roles = [], methods = {} } = options;

  return async (context, next) => {
    try {
      // Get session from Next-Auth
      const session = await auth();
      console.log("session", session);

      if (!session?.user) {
        throw ResponseUtils.unauthorized("Missing authentication");
      }

      // Get full user data from database including roles and permissions
      // const user = await db.query.users.findFirst({
      //   where: eq(users.email, session.user.email!),
      // });
      const { user } = session;

      if (user?.name !== process.env.AUTH_CREDENTIALS_USERNAME) {
        throw ResponseUtils.unauthorized("Missing authentication");
      }

      // if (!user) {
      //   throw ResponseUtils.unauthorized("User not found");
      // }

      // Get request method
      const method = context.req.method as HttpMethod;

      // Check global roles if specified
      // if (
      //   roles.length > 0 &&
      //   !roles.some((role) => user.roles?.includes(role))
      // ) {
      //   throw ResponseUtils.forbidden("Insufficient global role");
      // }

      // Check method-specific permissions
      const methodConfig = methods[method];
      if (methodConfig) {
        // Create validator context
        const validatorContext = {
          user,
          method,
          context,
        };

        // if (isRolesArray(methodConfig)) {
        //   if (!methodConfig.some((role) => user?.roles?.includes(role))) {
        //     throw ResponseUtils.forbidden(
        //       `Insufficient role for ${method} request`
        //     );
        //   }
        // } else if (isValidatorFn(methodConfig)) {
        //   const isValid = await methodConfig(validatorContext);
        //   if (!isValid) {
        //     throw ResponseUtils.forbidden(
        //       `Validation failed for ${method} request`
        //     );
        //   }
        // }
      }

      // Add user to context
      context.state.set("user", user);

      await next();
    } catch (error) {
      if (error instanceof Response) {
        throw error;
      }
      throw ResponseUtils.internalError("Authentication error");
    }
  };
}
