import { ResponseUtils } from "./response";
import type {
  FilterableField,
  PaginationConfig,
  FilterOperator,
  SchemaConfig,
} from "../types";
import { z } from "zod";
import { formatZodErrorSimple } from ".";

export class ValidationUtils {
  /**
   * 校验请求体
   */
  static async validateRequestBody(
    method: string,
    req: Request,
    schemaConfig: SchemaConfig
  ): Promise<Response | { validatedBody: any, originalBody: any }> {
    // GET 请求不需要验证 body
    if (method === "GET") {
      return { validatedBody: null, originalBody: null };
    }

    // DELETE 请求检查是否有 body（批量操作需要）
    if (method === "DELETE") {
      try {
        const body = await req.json();
        // 如果是批量删除（有 batch 字段），需要验证
        if (body && typeof body === 'object' && 'batch' in body) {
          return { validatedBody: body, originalBody: JSON.parse(JSON.stringify(body)) };
        }
        // 普通删除请求不应该有 body
        return { validatedBody: null, originalBody: null };
      } catch (error) {
        // 没有 body 的 DELETE 请求是正常的
        return { validatedBody: null, originalBody: null };
      }
    }

    // POST/PUT 请求必须提供对应的 schema
    if (method === "POST" && !schemaConfig.POST) {
      return ResponseUtils.methodNotAllowed(
        "POST method requires create schema"
      );
    }
    if (method === "PUT" && !schemaConfig.PUT) {
      return ResponseUtils.methodNotAllowed(
        "PUT method requires update schema"
      );
    }

    // 解析请求体
    let body;
    try {
      body = await req.json();
    } catch (error) {
      return ResponseUtils.badRequest("Invalid JSON in request body");
    }

    // 如果是数组，我们不进行验证，而是返回原始数组
    // 批量操作的验证将在executeBatchOperation中进行
    if (Array.isArray(body)) {
      return { validatedBody: body, originalBody: JSON.parse(JSON.stringify(body)) };
    }

    // 根据请求方法获取对应的 schema
    const schema = schemaConfig[method as keyof SchemaConfig];

    try {
      const validatedBody = await schema!.parseAsync(body);
      return { validatedBody, originalBody: JSON.parse(JSON.stringify(body)) };
    } catch (error) {
      if (error instanceof z.ZodError) {
        return ResponseUtils.badRequest(formatZodErrorSimple(error));
      }
      throw error;
    }
  }

  /**
   * 校验查询参数
   */
  static validateQueryParams(
    parsedParams: {
      search?: string;
      filter: Record<string, any>;
      sortFields: Array<{ field: string; order: "asc" | "desc" }>;
      pagination: { page?: number; limit?: number };
    },
    options: {
      filterableFields: Record<string, FilterableField>;
      sortableFields: string[];
      pagination: PaginationConfig;
    }
  ): Response | true {
    // 1. 校验过滤字段
    const filterValidation = this.validateFilterFields(
      parsedParams.filter,
      options.filterableFields
    );
    if (filterValidation instanceof Response) {
      return filterValidation;
    }

    // 2. 校验排序字段
    const sortValidation = this.validateSortFields(
      parsedParams.sortFields,
      options.sortableFields
    );
    if (sortValidation instanceof Response) {
      return sortValidation;
    }

    // 3. 校验分页参数
    const paginationValidation = this.validatePagination(
      parsedParams.pagination,
      options.pagination
    );
    if (paginationValidation instanceof Response) {
      return paginationValidation;
    }

    return true;
  }

  /**
   * 校验过滤字段
   */
  private static validateFilterFields(
    filter: Record<string, any>,
    filterableFields: Record<string, FilterableField>
  ): Response | true {
    for (const [key, value] of Object.entries(filter)) {
      // 校验字段是否允许
      if (!filterableFields[key]) {
        return ResponseUtils.badRequest(
          `Invalid filter field: ${key}. Allowed fields: ${Object.keys(
            filterableFields
          ).join(", ")}`
        );
      }

      // 校验操作符
      const operators = filterableFields[key].operators || ["eq"];
      const usedOperator = Object.keys(value)[0] as FilterOperator;
      if (!operators.includes(usedOperator)) {
        return ResponseUtils.badRequest(
          `Invalid operator '${usedOperator}' for field '${key}'. Allowed operators: ${operators.join(
            ", "
          )}`
        );
      }

      // 校验操作符值
      const operatorValidation = this.validateOperatorValue(
        key,
        usedOperator,
        value[usedOperator],
        filterableFields[key].schema
      );
      if (operatorValidation instanceof Response) {
        return operatorValidation;
      }
    }

    return true;
  }

  /**
   * 校验操作符值
   */
  private static validateOperatorValue(
    field: string,
    operator: FilterOperator,
    value: any,
    schema?: z.ZodType<any>
  ): Response | true {
    // 校验 'in' 操作符的值必须是数组
    if (operator === "in" && !Array.isArray(value)) {
      return ResponseUtils.badRequest(
        `Invalid value for 'in' operator on field '${field}'. Expected an array but got: ${typeof value}`
      );
    }

    // 使用 schema 验证值
    if (schema) {
      try {
        if (operator === "in") {
          // 对数组中的每个值进行验证
          for (const item of value) {
            schema.parse(item);
          }
        } else {
          schema.parse(value);
        }
      } catch (error) {
        if (error instanceof z.ZodError) {
          return ResponseUtils.badRequest(formatZodErrorSimple(error));
        }
        throw error;
      }
    }

    return true;
  }

  /**
   * 校验排序字段
   */
  private static validateSortFields(
    sortFields: Array<{ field: string; order: "asc" | "desc" }>,
    allowedFields: string[]
  ): Response | true {
    if (sortFields.length > 0) {
      const invalidFields = sortFields
        .map(({ field }) => field)
        .filter((field) => !allowedFields.includes(field));

      if (invalidFields.length > 0) {
        return ResponseUtils.badRequest(
          `Invalid sort fields: ${invalidFields.join(
            ", "
          )}. Allowed fields: ${allowedFields.join(", ")}`
        );
      }
    }

    return true;
  }

  /**
   * 校验分页参数
   */
  private static validatePagination(
    pagination: { page?: number; limit?: number },
    config: PaginationConfig
  ): Response | true {
    const { page, limit } = pagination;

    if (page !== undefined && (isNaN(page) || page < 1)) {
      return ResponseUtils.badRequest("Invalid page number");
    }

    if (
      limit !== undefined &&
      (isNaN(limit) || limit < 1 || limit > config.maxLimit)
    ) {
      return ResponseUtils.badRequest(
        `Invalid limit number. Maximum allowed: ${config.maxLimit}`
      );
    }

    return true;
  }

  /**
   * 校验路由参数
   */
  static validateRouteParams(
    params: Record<string, string>,
    schemas: Record<string, z.ZodType<any>>
  ): Response | Record<string, any> {
    const validatedParams: Record<string, any> = {};
    for (const [key, schema] of Object.entries(schemas)) {
      const value = params[key];
      if (!value) {
        return ResponseUtils.badRequest(`Missing required parameter: ${key}`);
      }
      validatedParams[key] = schema.parse(value);
    }
    return validatedParams;
  }
}
