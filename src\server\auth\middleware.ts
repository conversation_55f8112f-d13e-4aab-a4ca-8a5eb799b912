import type { Context } from "hono";
import { auth } from "./index";
import type { HttpMethod } from "../types";
import type { AuthCheckInput, AuthCheckResult, AuthConfig, RouteRule } from "./types";

// Export the proper middleware class
export { AuthenticationMiddleware } from "./AuthenticationMiddleware";

// Inject session & user into Hono context
export async function honoAuthSessionMiddleware(c: Context, next: Function) {
  const session = await auth.api.getSession({ headers: c.req.raw.headers });
  if (!session) {
    c.set("user", null);
    c.set("session", null);
    return next();
  }
  c.set("user", session.user);
  c.set("session", session.session);
  return next();
}

// Simple require auth guard
export function requireAuth() {
  return async (c: Context, next: Function) => {
    const user = c.get("user");
    if (!user) return c.json({ success: false, error: "UNAUTHORIZED" }, 401);
    return next();
  };
}

export function requireRoles(roles: string[]) {
  return async (c: Context, next: Function) => {
    const user = c.get("user");
    if (!user) return c.json({ success: false, error: "UNAUTHORIZED" }, 401);
    const has = Array.isArray(user?.roles) && roles.some((r) => user.roles.includes(r));
    if (!has) return c.json({ success: false, error: "FORBIDDEN" }, 403);
    return next();
  };
}

export function requireScopes(scopes: string[]) {
  return async (c: Context, next: Function) => {
    const user = c.get("user");
    if (!user) return c.json({ success: false, error: "UNAUTHORIZED" }, 401);
    const ok = Array.isArray(user?.scopes) && scopes.every((s) => user.scopes.includes(s));
    if (!ok) return c.json({ success: false, error: "FORBIDDEN" }, 403);
    return next();
  };
}

// Convention-first rule resolver
function resolveRouteRule(method: HttpMethod, path: string, resourceName?: string, config?: AuthConfig): RouteRule {
  // 1) Path / resource convention: admin area requires admin
  if (path.includes("/admin/") || resourceName?.includes("admin")) {
    return { required: true, roles: ["admin"] };
  }
  // 2) Method convention: GET public, others require auth
  if (method === "GET") return { required: false };
  return { required: true };
}

// Low-level auth check used by both Builder and custom helper
export async function checkAuth(input: AuthCheckInput): Promise<AuthCheckResult> {
  const { method, path, headers, resourceName, config, params } = input;

  // Apply convention
  const routeRule: RouteRule = {
    ...resolveRouteRule(method, path, resourceName, config),
    ...(config?.routes?.[method] ?? {}),
    ...(config?.globalAuth ?? {}),
  };

  // If not required → authorized
  if (!routeRule.required) return { authorized: true };

  // Authenticate via better-auth (auto: cookie or bearer)
  const session = await auth.api.getSession({ headers });
  if (!session) return { authorized: false, reason: "Authentication required" };

  const user = session.user;

  // Roles (using type assertion for extended user properties)
  if (routeRule.roles && (!Array.isArray((user as any)?.roles) || !routeRule.roles.some(r => (user as any).roles.includes(r)))) {
    return { authorized: false, user, session: session.session, reason: "Insufficient roles" };
  }
  // Scopes (using type assertion for extended user properties)
  if (routeRule.scopes && (!Array.isArray((user as any)?.scopes) || !routeRule.scopes.every(s => (user as any).scopes.includes(s)))) {
    return { authorized: false, user, session: session.session, reason: "Insufficient scopes" };
  }
  // Custom check
  if (routeRule.customCheck) {
    const ok = await routeRule.customCheck(user, { params: params ?? {}, method, path });
    if (!ok) return { authorized: false, user, session: session.session, reason: "Custom authorization failed" };
  }

  return { authorized: true, user, session: session.session };
}

// withAuth wrapper for custom routes
export function withAuth(handler: (c: Context) => any, overrides?: RouteRule) {
  return async (c: Context) => {
    const result = await checkAuth({
      method: c.req.method as HttpMethod,
      path: new URL(c.req.url).pathname,
      headers: c.req.raw.headers,
      params: c.req.param(),
      config: overrides ? { routes: { [c.req.method as HttpMethod]: overrides }, globalAuth: overrides } : undefined,
    });
    if (!result.authorized) {
      if (result.reason === "Authentication required") return c.json({ success: false, error: "UNAUTHORIZED" }, 401);
      return c.json({ success: false, error: "FORBIDDEN", message: result.reason }, 403);
    }
    // inject into c for downstream
    if (result.user) c.set("user", result.user);
    if (result.session) c.set("session", result.session);
    return handler(c);
  };
}

