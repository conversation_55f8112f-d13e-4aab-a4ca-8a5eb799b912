import type { Middleware, LoggerMiddlewareOptions } from "../types";

export function createLoggerMiddleware(
  options: LoggerMiddlewareOptions = {}
): Middleware {
  const { level = "info", format = "json" } = options;

  return async (context, next) => {
    const startTime = Date.now();
    const { req } = context;
    const url = new URL(req.url);

    // Log request
    const requestLog = {
      level,
      type: "request",
      method: req.method,
      path: url.pathname,
      query: Object.fromEntries(url.searchParams),
      timestamp: new Date().toISOString(),
    };

    if (format === "json") {
      console.log(JSON.stringify(requestLog));
    } else {
      console.log(
        `[${requestLog.timestamp}] ${requestLog.method} ${requestLog.path}`
      );
    }

    try {
      // Execute next middleware
      await next();

      // Log response
      const responseTime = Date.now() - startTime;
      const responseLog = {
        level,
        type: "response",
        method: req.method,
        path: url.pathname,
        status: context.response?.status || 200,
        duration: responseTime,
        timestamp: new Date().toISOString(),
      };

      if (format === "json") {
        console.log(JSON.stringify(responseLog));
      } else {
        console.log(
          `[${responseLog.timestamp}] ${responseLog.method} ${responseLog.path} ${responseLog.status} ${responseLog.duration}ms`
        );
      }
    } catch (error) {
      // Log error
      const errorLog = {
        level: "error",
        type: "error",
        method: req.method,
        path: url.pathname,
        error: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      };

      if (format === "json") {
        console.error(JSON.stringify(errorLog));
      } else {
        console.error(
          `[${errorLog.timestamp}] ERROR ${errorLog.method} ${errorLog.path} - ${errorLog.error}`
        );
      }

      throw error;
    }
  };
}
