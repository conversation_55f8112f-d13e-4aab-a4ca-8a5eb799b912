import type { CacheStorage } from "./storage";

export interface CacheOptions {
  ttl?: number;
  backfill?: boolean;
  priority?: number;
}

export interface CacheStats {
  hits: number;
  misses: number;
  size: number;
  layers: Array<{
    name: string;
    size: number;
    maxSize?: number;
  }>;
  backfills: number;
}

export class CacheManager {
  private storages: CacheStorage[] = [];
  private stats = {
    hits: 0,
    misses: 0,
    backfills: 0,
  };

  constructor(
    storages: Array<{ name: string; storage: CacheStorage }>
  ) {
    console.log('CacheManager:', new Date().toISOString())
    this.storages = storages
      .map((s) => s.storage)
      .sort((a, b) => (a.priority ?? 0) - (b.priority ?? 0));
  }

  private async backfillStorages(
    key: string,
    value: any,
    fromIndex: number,
    options: CacheOptions = {}
  ) {
    const backfillPromises: Promise<void>[] = [];

    for (let i = 0; i < fromIndex; i++) {
      const storage = this.storages[i];
      if (storage.backfill && (options.backfill ?? true)) {
        backfillPromises.push(storage.set(key, value, options.ttl));
      }
    }

    if (backfillPromises.length > 0) {
      await Promise.all(backfillPromises);
      this.stats.backfills += backfillPromises.length;
    }
  }

  async get(key: string): Promise<any> {
    for (let i = 0; i < this.storages.length; i++) {
      const storage = this.storages[i];
      const value = await storage.get(key);

      if (value !== null) {
        this.stats.hits++;
        await this.backfillStorages(key, value, i);
        return value;
      }
    }

    this.stats.misses++;
    return null;
  }

  async set(
    key: string,
    value: any,
    options: CacheOptions = {}
  ): Promise<void> {
    const setPromises = this.storages
      .filter(
        (storage) =>
          options.priority === undefined ||
          (storage.priority ?? 0) <= options.priority
      )
      .map((storage) => storage.set(key, value, options.ttl));

    await Promise.all(setPromises);
  }

  async delete(key: string): Promise<void> {
    await Promise.all(this.storages.map((storage) => storage.delete(key)));
  }

  async invalidateResource(resourceName: string): Promise<void> {
    await this.delete(resourceName);
  }

  getStats(): CacheStats {
    return {
      hits: this.stats.hits,
      misses: this.stats.misses,
      backfills: this.stats.backfills,
      size: this.storages.reduce(
        (total, storage) => total + (storage.getStats?.()?.size || 0),
        0
      ),
      layers: this.storages.map((storage, index) => ({
        name: `layer-${index}`,
        size: storage.getStats?.()?.size || 0,
        maxSize: storage.getStats?.()?.maxSize,
      })),
    };
  }

  async clear(): Promise<void> {
    await Promise.all(this.storages.map((storage) => storage.clear()));
    this.stats = { hits: 0, misses: 0, backfills: 0 };
  }
}

export const createCacheManager = (
  storages: Array<{ name: string; storage: CacheStorage }>
) => new CacheManager(storages);
