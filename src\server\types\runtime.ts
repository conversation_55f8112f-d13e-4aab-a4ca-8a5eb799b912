/**
 * 运行时相关的类型定义
 * 避免直接依赖特定框架的类型
 */

// Next.js 请求类型的条件定义
export type NextRequest = {
  method: string;
  url: string;
  headers: Headers;
  json(): Promise<any>;
  text(): Promise<string>;
  formData(): Promise<FormData>;
  clone(): NextRequest;
} | any; // 如果 next/server 不可用，回退到 any

// Next.js 响应类型的条件定义
export type NextResponse = {
  json(data: any, init?: ResponseInit): Response;
  redirect(url: string, status?: number): Response;
} | any;

// Next.js 响应类的静态方法类型
export interface NextResponseStatic {
  json(data: any, init?: ResponseInit): Response;
  redirect(url: string, status?: number): Response;
}

// Hono Context 类型的条件定义
export type HonoContext = {
  req: {
    method: string;
    url: string;
    param(): Record<string, string>;
    param(key: string): string | undefined;
    json(): Promise<any>;
    text(): Promise<string>;
  };
  json(data: any, status?: number): Response;
  text(text: string, status?: number): Response;
} | any;

// 通用请求接口
export interface UniversalRequest {
  method: string;
  url: string;
  headers: Record<string, string> | Headers;
  json(): Promise<any>;
  text(): Promise<string>;
}

// 运行时检测工具
export function isNextRequest(req: any): req is NextRequest {
  return req && typeof req.headers?.get === 'function';
}

export function isHonoContext(ctx: any): ctx is HonoContext {
  return ctx && typeof ctx.req?.param === 'function';
}
