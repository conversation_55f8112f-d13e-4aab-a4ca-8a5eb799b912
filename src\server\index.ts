/**
 * API 构建器主入口
 *
 * 注意：这是一个通用入口，建议根据项目类型使用专门的入口：
 * - Hono 项目: import from '@/server/builders/hono'
 * - Next.js 项目: import from '@/server/builders/next'
 */

import type { SQLiteTableWithColumns } from "drizzle-orm/sqlite-core";
import type { Middleware } from "./types";
import { MiddlewareManager, createMiddlewareManager } from "./core/middleware";

// 通用导出
export * from "./types";
export * from "./utils";
export * from "./core/middleware";
export * from "./db";

// 基础构建器导出
export { BaseApiRouteBuilder } from "./builders/base-route-builder";
export { QueryBuilder, createQueryBuilder } from "./builders/query-builder";

// 框架特定的构建器 - 建议使用专门的入口点
export { ApiRouteBuilder } from "./builders/route-builder"; // Next.js
export { HonoApiRouteBuilder } from "./builders/hono-route-builder"; // Hono

export class ApiEngine {
  private middlewareManager: MiddlewareManager = createMiddlewareManager();

  middleware(middleware: Middleware, middlewareId: string) {
    this.middlewareManager.use(middleware, middlewareId);
    return this;
  }

  resource(name: string, table: SQLiteTableWithColumns<any>) {
    // 动态导入以避免硬依赖
    const { ApiRouteBuilder } = require("./builders/route-builder");
    const builder = new ApiRouteBuilder().resource(name, table);

    this.middlewareManager.getMiddlewares().forEach((middleware) => {
      builder.middleware(middleware.middleware, middleware.id);
    });

    return builder;
  }
}
