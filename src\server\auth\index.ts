// Better-Auth server instance (framework-agnostic usage from server layer)
// NOTE: This file wires better-auth with minimal defaults; business extensions belong to src/app/auth

import { betterAuth } from "better-auth";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import { db } from "../../app/db";
import { rateLimitConfig } from "./rate-limit-config";

export const auth = betterAuth({
  database: drizzleAdapter(db, {
    provider: "sqlite", // 使用 SQLite 提供者
  }),
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: false, // 开发阶段先关闭邮箱验证
  },
  secret: process.env.BETTER_AUTH_SECRET as string,
  baseURL: process.env.BETTER_AUTH_URL as string,
  basePath: "/api/auth", // 明确设置基础路径

  // 集成率限制配置
  rateLimit: rateLimitConfig,

  // 配置 IP 地址识别（用于率限制）
  advanced: {
    ipAddress: {
      ipAddressHeaders: ["cf-connecting-ip", "x-forwarded-for", "x-real-ip"],
    },
  },

  // MVP: rely on better-auth defaults; Google provider configured in business layer via env
  // socialProviders: {
  //   google: {
  //     clientId: process.env.GOOGLE_CLIENT_ID as string,
  //     clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
  //   },
  // },
});

export type ServerSession = typeof auth.$Infer.Session;
export type ServerUser = typeof auth.$Infer.Session.user;

