<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>积分系统测试 - Credit System Test</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }

        .section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            border: 1px solid #e9ecef;
        }

        .section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5rem;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #34495e;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #3498db;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s;
            margin: 5px;
            min-width: 120px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn-success { background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%); }
        .btn-warning { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .btn-danger { background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%); }
        .btn-info { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .btn-premium { background: linear-gradient(135deg, #ffd89b 0%, #19547b 100%); }

        .user-info {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .credit-display {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 20px;
        }

        .credit-balance {
            font-size: 2.5rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .message {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: 500;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .message.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .test-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 20px;
        }

        .operation-group {
            background: white;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #ddd;
        }

        .operation-group h3 {
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .transaction-history {
            max-height: 300px;
            overflow-y: auto;
            background: white;
            border-radius: 8px;
            padding: 15px;
        }

        .transaction-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .transaction-item:last-child {
            border-bottom: none;
        }

        .transaction-amount {
            font-weight: bold;
        }

        .transaction-amount.positive {
            color: #27ae60;
        }

        .transaction-amount.negative {
            color: #e74c3c;
        }

        .full-width {
            grid-column: 1 / -1;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }

        .batch-input {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-bottom: 10px;
        }

        .batch-input input {
            flex: 1;
            margin: 0;
        }

        .response-headers {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏦 积分系统测试平台</h1>
            <p>Credit System Testing Platform - Better Auth Integration</p>
        </div>

        <div class="main-content">
            <!-- 认证部分 -->
            <div class="section">
                <h2>🔐 用户认证</h2>
                
                <!-- 未登录状态 -->
                <div id="loginSection">
                    <div class="form-group">
                        <label for="loginEmail">邮箱地址:</label>
                        <input type="email" id="loginEmail" placeholder="输入邮箱地址" value="<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label for="loginPassword">密码:</label>
                        <input type="password" id="loginPassword" placeholder="输入密码" value="password123">
                    </div>
                    <button class="btn btn-success" onclick="login()">登录</button>
                    <button class="btn btn-info" onclick="register()">注册新用户</button>
                </div>

                <!-- 已登录状态 -->
                <div id="userSection" class="hidden">
                    <div class="user-info">
                        <h3>👤 当前用户</h3>
                        <div id="userInfo"></div>
                        <button class="btn btn-warning" onclick="logout()">退出登录</button>
                    </div>
                </div>

                <!-- 消息显示区域 -->
                <div id="authMessages"></div>
            </div>

            <!-- 积分管理部分 -->
            <div class="section">
                <h2>💰 积分管理</h2>
                
                <div class="credit-display">
                    <div class="credit-balance" id="creditBalance">--</div>
                    <div>当前积分余额</div>
                </div>

                <div class="form-group">
                    <label for="creditAmount">测试用积分调整:</label>
                    <div style="display: flex; gap: 10px;">
                        <input type="number" id="creditAmount" placeholder="积分数量" value="100">
                        <button class="btn btn-info" onclick="addCredits()">添加积分</button>
                    </div>
                </div>

                <div id="creditMessages"></div>
            </div>

            <!-- 功能测试部分 -->
            <div class="section full-width">
                <h2>🧪 功能测试</h2>
                
                <div class="test-buttons">
                    <div class="operation-group">
                        <h3>基础操作</h3>
                        <button class="btn btn-success" onclick="testOperation('light', 1)">
                            轻量操作 (1积分)
                        </button>
                        <button class="btn btn-info" onclick="testOperation('standard', 5)">
                            标准操作 (5积分)
                        </button>
                        <button class="btn btn-danger" onclick="testOperation('heavy', 10)">
                            重量操作 (10积分)
                        </button>
                    </div>

                    <div class="operation-group">
                        <h3>高级操作</h3>
                        <div class="batch-input">
                            <input type="number" id="batchQuantity" placeholder="数量" value="5" min="1" max="50">
                            <button class="btn btn-warning" onclick="testBatchOperation()">
                                批量操作
                            </button>
                        </div>
                        <button class="btn btn-premium" onclick="testOperation('premium', 25)">
                            高级功能 (25积分)
                        </button>
                    </div>
                </div>

                <div id="testMessages"></div>
                <div id="responseHeaders" class="response-headers hidden"></div>
            </div>

            <!-- 交易历史部分 -->
            <div class="section full-width">
                <h2>📊 交易历史</h2>
                <div class="transaction-history" id="transactionHistory">
                    <div style="text-align: center; color: #666;">暂无交易记录</div>
                </div>
                <button class="btn btn-info" onclick="refreshTransactionHistory()">刷新历史</button>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000';
        let currentUser = null;
        let currentCredits = 0;
        let transactions = [];

        // 页面加载时检查登录状态
        window.addEventListener('load', async () => {
            await checkSession();
        });

        // 显示消息
        function showMessage(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = message;
            container.appendChild(messageDiv);
            
            setTimeout(() => {
                messageDiv.remove();
            }, 5000);
        }

        // 检查会话状态
        async function checkSession() {
            try {
                const response = await fetch(`${API_BASE}/api/auth/get-session`, {
                    credentials: 'include'
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.user) {
                        currentUser = data.user;
                        updateUserDisplay();
                        await updateCreditBalance();
                        return;
                    }
                }
            } catch (error) {
                console.error('Session check failed:', error);
            }
            
            // 未登录状态
            showLoginForm();
        }

        // 显示登录表单
        function showLoginForm() {
            document.getElementById('loginSection').classList.remove('hidden');
            document.getElementById('userSection').classList.add('hidden');
            currentUser = null;
            updateCreditDisplay(0);
        }

        // 更新用户显示
        function updateUserDisplay() {
            document.getElementById('loginSection').classList.add('hidden');
            document.getElementById('userSection').classList.remove('hidden');
            
            const userInfo = document.getElementById('userInfo');
            userInfo.innerHTML = `
                <p><strong>姓名:</strong> ${currentUser.name}</p>
                <p><strong>邮箱:</strong> ${currentUser.email}</p>
                <p><strong>ID:</strong> ${currentUser.id}</p>
                <p><strong>注册时间:</strong> ${new Date(currentUser.createdAt).toLocaleString()}</p>
            `;
        }

        // 登录
        async function login() {
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;

            if (!email || !password) {
                showMessage('authMessages', '请填写邮箱和密码', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/api/auth/sign-in/email`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                    body: JSON.stringify({ email, password })
                });

                const data = await response.json();
                
                if (response.ok) {
                    showMessage('authMessages', '登录成功！', 'success');
                    currentUser = data.user;
                    updateUserDisplay();
                    await updateCreditBalance();
                } else {
                    showMessage('authMessages', `登录失败: ${data.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                showMessage('authMessages', `登录失败: ${error.message}`, 'error');
            }
        }

        // 注册
        async function register() {
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;

            if (!email || !password) {
                showMessage('authMessages', '请填写邮箱和密码', 'error');
                return;
            }

            const name = email.split('@')[0]; // 使用邮箱前缀作为姓名

            try {
                const response = await fetch(`${API_BASE}/api/auth/sign-up/email`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                    body: JSON.stringify({ name, email, password })
                });

                const data = await response.json();
                
                if (response.ok) {
                    showMessage('authMessages', '注册成功！', 'success');
                    currentUser = data.user;
                    updateUserDisplay();
                    await updateCreditBalance();
                } else {
                    showMessage('authMessages', `注册失败: ${data.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                showMessage('authMessages', `注册失败: ${error.message}`, 'error');
            }
        }

        // 退出登录
        async function logout() {
            try {
                const response = await fetch(`${API_BASE}/api/auth/sign-out`, {
                    method: 'POST',
                    credentials: 'include'
                });

                if (response.ok) {
                    showMessage('authMessages', '退出登录成功！', 'success');
                    showLoginForm();
                    transactions = [];
                    updateTransactionHistory();
                } else {
                    showMessage('authMessages', '退出登录失败', 'error');
                }
            } catch (error) {
                showMessage('authMessages', `退出登录失败: ${error.message}`, 'error');
            }
        }

        // 更新积分余额显示
        function updateCreditDisplay(credits) {
            currentCredits = credits;
            document.getElementById('creditBalance').textContent = credits.toLocaleString();
        }

        // 获取积分余额
        async function updateCreditBalance() {
            if (!currentUser) return;

            try {
                const response = await fetch(`${API_BASE}/api/credit-test/balance`, {
                    credentials: 'include'
                });

                if (response.ok) {
                    const data = await response.json();
                    updateCreditDisplay(data.credits || 0);
                } else {
                    // 如果用户没有积分记录，默认为 0
                    updateCreditDisplay(0);
                }
            } catch (error) {
                console.error('Failed to update credit balance:', error);
                updateCreditDisplay(0);
            }
        }

        // 添加积分（测试用）
        async function addCredits() {
            const amount = parseInt(document.getElementById('creditAmount').value);

            if (!amount || amount <= 0 || amount > 1000) {
                showMessage('creditMessages', '请输入有效的积分数量 (1-1000)', 'error');
                return;
            }

            if (!currentUser) {
                showMessage('creditMessages', '请先登录', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/api/credit-test/add-credits`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                    body: JSON.stringify({ amount })
                });

                if (response.ok) {
                    const data = await response.json();
                    updateCreditDisplay(data.newCredits);
                    addTransaction(`添加积分`, amount, data.newCredits);
                    showMessage('creditMessages', `成功添加 ${amount} 积分`, 'success');
                } else {
                    const errorData = await response.json();
                    showMessage('creditMessages', `添加积分失败: ${errorData.error}`, 'error');
                }
            } catch (error) {
                showMessage('creditMessages', `添加积分失败: ${error.message}`, 'error');
            }
        }

        // 测试操作
        async function testOperation(type, cost) {
            if (!currentUser) {
                showMessage('testMessages', '请先登录', 'error');
                return;
            }

            const button = event.target;
            const originalText = button.textContent;
            button.disabled = true;
            button.innerHTML = '<span class="loading"></span> 处理中...';

            try {
                let endpoint = `${API_BASE}/api/credit-test/${type}`;
                let method = 'GET';
                let body = null;

                // 根据操作类型选择不同的 API 端点和方法
                switch (type) {
                    case 'light':
                        method = 'GET';
                        break;
                    case 'standard':
                        method = 'POST';
                        body = JSON.stringify({
                            data: `Test data ${Date.now()}`,
                            operation: 'standard'
                        });
                        break;
                    case 'heavy':
                        method = 'DELETE';
                        break;
                    case 'premium':
                        method = 'GET';
                        break;
                }

                const options = {
                    method,
                    credentials: 'include',
                    headers: {}
                };

                if (body) {
                    options.headers['Content-Type'] = 'application/json';
                    options.body = body;
                }

                const response = await fetch(endpoint, options);

                // 显示响应头信息
                showResponseHeaders(response);

                if (response.ok) {
                    const data = await response.json();
                    const creditsUsed = response.headers.get('X-Credits-Used');
                    const creditsRemaining = response.headers.get('X-Credits-Remaining');

                    if (creditsUsed && creditsRemaining) {
                        updateCreditDisplay(parseInt(creditsRemaining));
                        addTransaction(`${data.description || type + '操作'}`, -parseInt(creditsUsed), parseInt(creditsRemaining));
                        showMessage('testMessages',
                            `${data.description || '操作'}成功！消耗 ${creditsUsed} 积分，剩余 ${creditsRemaining} 积分`, 'success');
                    } else {
                        showMessage('testMessages', `${data.description || '操作'}成功！`, 'success');
                    }
                } else {
                    const errorData = await response.text();
                    showMessage('testMessages', `操作失败: ${errorData}`, 'error');
                }
            } catch (error) {
                showMessage('testMessages', `操作失败: ${error.message}`, 'error');
            } finally {
                button.disabled = false;
                button.textContent = originalText;
            }
        }

        // 测试批量操作
        async function testBatchOperation() {
            const quantity = parseInt(document.getElementById('batchQuantity').value);

            if (!quantity || quantity <= 0 || quantity > 50) {
                showMessage('testMessages', '请输入有效的数量 (1-50)', 'error');
                return;
            }

            if (!currentUser) {
                showMessage('testMessages', '请先登录', 'error');
                return;
            }

            const button = event.target;
            const originalText = button.textContent;
            button.disabled = true;
            button.innerHTML = '<span class="loading"></span> 处理中...';

            try {
                const response = await fetch(`${API_BASE}/api/credit-test/batch`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                    body: JSON.stringify({ quantity })
                });

                showResponseHeaders(response);

                if (response.ok) {
                    const data = await response.json();
                    const creditsUsed = response.headers.get('X-Credits-Used');
                    const creditsRemaining = response.headers.get('X-Credits-Remaining');

                    if (creditsUsed && creditsRemaining) {
                        updateCreditDisplay(parseInt(creditsRemaining));
                        addTransaction(`批量操作 (${quantity}项)`, -parseInt(creditsUsed), parseInt(creditsRemaining));
                        showMessage('testMessages',
                            `批量操作成功！处理 ${quantity} 项，消耗 ${creditsUsed} 积分，剩余 ${creditsRemaining} 积分`, 'success');
                    } else {
                        showMessage('testMessages', `批量操作成功！处理 ${quantity} 项`, 'success');
                    }
                } else {
                    const errorData = await response.text();
                    showMessage('testMessages', `批量操作失败: ${errorData}`, 'error');
                }
            } catch (error) {
                showMessage('testMessages', `批量操作失败: ${error.message}`, 'error');
            } finally {
                button.disabled = false;
                button.textContent = originalText;
            }
        }

        // 显示响应头
        function showResponseHeaders(response) {
            const headersDiv = document.getElementById('responseHeaders');
            const relevantHeaders = ['X-Credits-Remaining', 'X-Credits-Used', 'X-RateLimit-Remaining'];
            
            let headerText = '响应头信息:\n';
            relevantHeaders.forEach(header => {
                const value = response.headers.get(header);
                if (value !== null) {
                    headerText += `${header}: ${value}\n`;
                }
            });

            if (headerText === '响应头信息:\n') {
                headersDiv.classList.add('hidden');
            } else {
                headersDiv.textContent = headerText;
                headersDiv.classList.remove('hidden');
            }
        }

        // 添加交易记录
        function addTransaction(description, amount, balance) {
            transactions.unshift({
                id: Date.now(),
                description,
                amount,
                balance,
                timestamp: new Date()
            });

            // 只保留最近 20 条记录
            if (transactions.length > 20) {
                transactions = transactions.slice(0, 20);
            }

            updateTransactionHistory();
        }

        // 更新交易历史显示
        function updateTransactionHistory() {
            const container = document.getElementById('transactionHistory');
            
            if (transactions.length === 0) {
                container.innerHTML = '<div style="text-align: center; color: #666;">暂无交易记录</div>';
                return;
            }

            container.innerHTML = transactions.map(transaction => `
                <div class="transaction-item">
                    <div>
                        <div style="font-weight: 500;">${transaction.description}</div>
                        <div style="font-size: 12px; color: #666;">
                            ${transaction.timestamp.toLocaleString()}
                        </div>
                    </div>
                    <div>
                        <div class="transaction-amount ${transaction.amount >= 0 ? 'positive' : 'negative'}">
                            ${transaction.amount >= 0 ? '+' : ''}${transaction.amount}
                        </div>
                        <div style="font-size: 12px; color: #666;">
                            余额: ${transaction.balance}
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 刷新交易历史
        function refreshTransactionHistory() {
            updateTransactionHistory();
            showMessage('testMessages', '交易历史已刷新', 'info');
        }
    </script>
</body>
</html>
