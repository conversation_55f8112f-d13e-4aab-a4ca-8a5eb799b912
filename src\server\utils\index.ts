export * from "./response";
export * from "./query";
export * from "./validation";
import { ZodError } from "zod";

// zod 错误转为字符串显示
export function formatZodErrorSimple(error: ZodError) {
  if (!error || !error.issues) {
    return 'No Zod errors found.';
  }

  return error.issues.map((issue: any) => {
    const pathString = issue.path?.length > 0 ? ` in field '${issue.path.join('.')}'` : '';
    return `Error${pathString}: ${issue.message} (Expected: ${issue?.expected}, Received: ${issue?.received})`;
  }).join('\n');
}
