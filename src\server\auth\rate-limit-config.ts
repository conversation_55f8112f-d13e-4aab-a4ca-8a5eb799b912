// Better Auth 率限制配置
import type { RateLimitConfig } from "better-auth/types";

/**
 * Better Auth 率限制配置
 * 替换自定义的 rate-limit.ts 实现
 */
export const rateLimitConfig: RateLimitConfig = {
  enabled: true,
  window: 60, // 60 秒窗口
  max: 100,   // 最多 100 个请求
  
  // 针对不同端点的自定义规则
  customRules: {
    // 认证相关端点 - 更严格的限制
    "/api/auth/sign-in/email": {
      window: 60,
      max: 5, // 每分钟最多 5 次登录尝试
    },
    "/api/auth/sign-up/email": {
      window: 300, // 5 分钟
      max: 3,      // 最多 3 次注册尝试
    },
    "/api/auth/forgot-password": {
      window: 300,
      max: 2,
    },
    
    // API 端点 - 按操作类型分级
    "/api/auto-*": async (request: Request) => {
      const method = request.method;
      const url = new URL(request.url);
      
      // 根据 HTTP 方法动态设置限制
      switch (method) {
        case 'GET':
          return { window: 60, max: 200 }; // 读操作更宽松
        case 'POST':
          return { window: 60, max: 50 };  // 写操作更严格
        case 'PUT':
        case 'DELETE':
          return { window: 60, max: 20 };  // 修改/删除操作最严格
        default:
          return { window: 60, max: 100 };
      }
    },
    
    // 高频操作端点
    "/api/v1/usage-logs": {
      window: 60,
      max: 500, // 使用日志可能很频繁
    },
  },
  
  // 使用数据库存储（与认证数据一致）
  storage: "database",
  modelName: "rateLimit",
};
