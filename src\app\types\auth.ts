import { z } from 'zod';
import type { users, apiKeys, userUsage } from '../schemas/auth';

// 导出数据库类型
export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;
export type ApiKey = typeof apiKeys.$inferSelect;
export type NewApiKey = typeof apiKeys.$inferInsert;
export type UserUsage = typeof userUsage.$inferSelect;
export type NewUserUsage = typeof userUsage.$inferInsert;

// Zod 验证 Schema
export const CreateUserSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100),
  email: z.string().email('Invalid email format'),
});

export const UpdateUserSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  email: z.string().email().optional(),
});

export const CreateApiKeySchema = z.object({
  name: z.string().min(1, 'API key name is required').max(100),
  expiresAt: z.number().optional(), // timestamp
});

export const UpdateApiKeySchema = z.object({
  name: z.string().min(1).max(100).optional(),
  expiresAt: z.number().optional(),
});

// API 请求/响应类型
export interface CreateUserRequest {
  name: string;
  email: string;
}

export interface UpdateUserRequest {
  name?: string;
  email?: string;
}

export interface CreateApiKeyRequest {
  name: string;
  expiresAt?: number;
}

export interface UpdateApiKeyRequest {
  name?: string;
  expiresAt?: number;
}

// 认证相关类型
export interface AuthContext {
  user?: User;
  apiKey?: ApiKey;
  isAuthenticated: boolean;
}

export interface UserWithUsage extends User {
  usage?: UserUsage;
  apiKeys?: ApiKey[];
}
