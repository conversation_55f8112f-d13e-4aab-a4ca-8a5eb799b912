import { z } from 'zod';
import type { Service, Key, UsageLog, ServiceConfig, KeyStatus } from '../schemas/key-rotation';

// Zod 验证 Schema
export const ServiceConfigSchema = z.object({
  rotation_strategy: z.object({
    max_usage: z.number().min(1).max(10000),
    selection_algorithm: z.enum(['round_robin', 'random', 'weighted', 'least_used'])
  }),
  pool_config: z.object({
    min_active_keys: z.number().min(1).default(1),
    auto_disable_failed: z.boolean().default(false)
  }),
  cache_config: z.object({
    enable_cache: z.boolean().default(true),
    cache_ttl_seconds: z.number().min(1).max(3600).default(30)
  })
});

export const CreateServiceSchema = z.object({
  name: z.string().min(1, 'Service name is required').max(100),
  description: z.string().optional(),
  config: ServiceConfigSchema
});

export const UpdateServiceSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  description: z.string().optional(),
  config: ServiceConfigSchema.partial().optional()
});

export const CreateKeySchema = z.object({
  key_value: z.string().min(1),
  max_usage: z.number().min(1).optional(),
  priority: z.number().min(0).max(10).default(1)
});

export const BatchCreateKeysSchema = z.object({
  keys: z.array(CreateKeySchema).min(1).max(1000)
});

export const AcquireKeySchema = z.object({
  algorithm: z.enum(['round_robin', 'random', 'weighted', 'least_used']).optional(),
  metadata: z.record(z.any()).optional()
});

export const ReleaseKeySchema = z.object({
  is_valid: z.boolean().default(true),
  metadata: z.record(z.any()).optional()
});

// API 请求/响应类型
export interface CreateServiceRequest {
  name: string;
  description?: string;
  config: ServiceConfig;
}

export interface UpdateServiceRequest {
  name?: string;
  description?: string;
  config?: Partial<ServiceConfig>;
}

export interface AcquireKeyRequest {
  algorithm?: 'round_robin' | 'random' | 'weighted' | 'least_used';
  metadata?: Record<string, any>;
}

export interface AcquireKeyResponse {
  success: boolean;
  data?: {
    key_id: string;
    key_value: string;
    usage_count: number;
    remaining_usage: number;
    expires_in_uses: number;
  };
  error?: string;
  message?: string;
  metadata: {
    service_id: string;
    algorithm_used: string;
    pool_status: {
      total_keys: number;
      active_keys: number;
      pool_health: 'healthy' | 'warning' | 'critical';
    };
    response_time_ms?: number;
  };
}

export interface ReleaseKeyRequest {
  is_valid?: boolean;
  metadata?: Record<string, any>;
}

export interface PoolStatus {
  total_keys: number;
  active_keys: number;
  exhausted_keys: number;
  invalid_keys: number;
  pool_health: 'healthy' | 'warning' | 'critical';
}

// 业务逻辑选项
export interface AcquireOptions {
  algorithm?: string;
  metadata?: Record<string, any>;
}

export interface AcquireResult {
  success: boolean;
  data?: {
    key_id: string;
    key_value: string;
    usage_count: number;
    remaining_usage: number;
    expires_in_uses: number;
  };
  error?: string;
  message?: string;
  metadata?: {
    service_id: string;
    algorithm_used: string;
    pool_status: PoolStatus;
    response_time_ms: number;
  };
}

// 错误类型
export class ValidationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ValidationError';
  }
}

export class KeyExhaustionError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'KeyExhaustionError';
  }
}

export class ServiceNotFoundError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ServiceNotFoundError';
  }
}
