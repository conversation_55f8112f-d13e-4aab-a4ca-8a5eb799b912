// 积分配置工厂函数
import type { CreditConfig, MethodCreditConfig, RouteCreditConfig } from "./credit-middleware";

/**
 * 标准积分配置选项
 */
export interface StandardCreditOptions {
  read?: number;
  write?: number;
  delete?: number;
  strategy?: 'pre' | 'post' | 'reserve';
  refundOnFailure?: boolean;
  minimumBalance?: number;
  skipForRoles?: string[];
}

/**
 * 动态积分配置选项
 */
export interface DynamicCreditOptions {
  baseRate?: number;
  perRecord?: number;
  maxCost?: number;
  method?: string;
}

/**
 * 积分配置构建器
 */
export class CreditConfigBuilder {
  private config: RouteCreditConfig = {};

  /**
   * 设置全局配置
   */
  global(config: Partial<CreditConfig>): this {
    this.config.global = { ...this.config.global, ...config };
    return this;
  }

  /**
   * 启用或禁用积分系统
   */
  enabled(enabled: boolean): this {
    return this.global({ enabled });
  }

  /**
   * 设置方法特定配置
   */
  method(method: string, config: CreditConfig): this {
    if (!this.config.methods) {
      this.config.methods = {};
    }
    this.config.methods[method] = config;
    return this;
  }

  /**
   * 设置路径特定配置
   */
  path(path: string, config: MethodCreditConfig): this {
    if (!this.config.paths) {
      this.config.paths = {};
    }
    this.config.paths[path] = config;
    return this;
  }

  /**
   * 设置动态配置函数
   */
  dynamic(fn: (context: any) => Promise<CreditConfig | null>): this {
    this.config.dynamic = fn;
    return this;
  }

  /**
   * 构建最终配置
   */
  build(): RouteCreditConfig {
    return this.config;
  }
}

/**
 * 创建标准的 CRUD 积分配置
 */
export function standardCredits(options: StandardCreditOptions = {}): RouteCreditConfig {
  const {
    read = 1,
    write = 5,
    delete: del = 10,
    strategy = 'post',
    refundOnFailure = true,
    minimumBalance = 0,
    skipForRoles = [],
  } = options;

  return new CreditConfigBuilder()
    .global({
      refundOnFailure,
      minimumBalance,
      skipForRoles,
    })
    .method('GET', {
      cost: read,
      deductionStrategy: strategy,
      refundOnFailure,
      minimumBalance,
      skipForRoles,
    })
    .method('POST', {
      cost: write,
      deductionStrategy: strategy,
      refundOnFailure: true,
      minimumBalance,
      skipForRoles,
    })
    .method('PUT', {
      cost: write,
      deductionStrategy: strategy,
      refundOnFailure: true,
      minimumBalance,
      skipForRoles,
    })
    .method('DELETE', {
      cost: del,
      deductionStrategy: 'pre',
      refundOnFailure: true,
      minimumBalance: Math.max(minimumBalance, del),
      skipForRoles,
    })
    .build();
}

/**
 * 创建基于数据量的动态积分配置
 */
export function dataBasedCredits(options: DynamicCreditOptions = {}): RouteCreditConfig {
  const {
    baseRate = 1,
    perRecord = 0.1,
    maxCost = 100,
    method = 'GET',
  } = options;

  return new CreditConfigBuilder()
    .method(method, {
      cost: async (context) => {
        const limit = parseInt(context.req.query?.limit || '10');
        const cost = Math.min(baseRate + (limit * perRecord), maxCost);
        return Math.ceil(cost);
      },
      deductionStrategy: 'post',
      refundOnFailure: true,
    })
    .build();
}

/**
 * 创建批量操作积分配置
 */
export function batchCredits(options: {
  baseCost?: number;
  perOperationCost?: number;
  maxCost?: number;
  discount?: number;
} = {}): RouteCreditConfig {
  const {
    baseCost = 5,
    perOperationCost = 0.5,
    maxCost = 100,
    discount = 0.8,
  } = options;

  return new CreditConfigBuilder()
    .method('POST', {
      cost: async (context) => {
        const { operations } = await context.req.json();
        
        if (!Array.isArray(operations)) {
          return baseCost;
        }
        
        const totalCost = baseCost + (operations.length * perOperationCost);
        return Math.min(Math.ceil(totalCost * discount), maxCost);
      },
      deductionStrategy: 'reserve',
      refundOnFailure: true,
      minimumBalance: 20,
    })
    .build();
}

/**
 * 创建分层定价积分配置
 */
export function tieredCredits(options: {
  free?: Partial<StandardCreditOptions>;
  premium?: Partial<StandardCreditOptions>;
  admin?: Partial<StandardCreditOptions>;
} = {}): RouteCreditConfig {
  return new CreditConfigBuilder()
    .dynamic(async (context) => {
      const user = context.state.get("user");
      if (!user) return null;

      const tierConfig = options[user.plan as keyof typeof options] || options.free;
      if (!tierConfig) return null;

      const {
        read = 2,
        write = 10,
        delete: del = 20,
        strategy = 'post',
      } = tierConfig;

      const method = context.req.method;
      switch (method) {
        case 'GET':
          return { cost: read, deductionStrategy: strategy };
        case 'POST':
        case 'PUT':
          return { cost: write, deductionStrategy: strategy, refundOnFailure: true };
        case 'DELETE':
          return { cost: del, deductionStrategy: 'pre', refundOnFailure: true };
        default:
          return null;
      }
    })
    .build();
}

/**
 * 创建路径特定的积分配置
 */
export function pathBasedCredits(pathConfigs: {
  [path: string]: StandardCreditOptions;
}): RouteCreditConfig {
  const builder = new CreditConfigBuilder();

  for (const [path, options] of Object.entries(pathConfigs)) {
    const config = standardCredits(options);
    if (config.methods) {
      builder.path(path, config.methods);
    }
  }

  return builder.build();
}

/**
 * 创建免费层限制配置
 */
export function freeTierCredits(): RouteCreditConfig {
  return tieredCredits({
    free: {
      read: 2,
      write: 10,
      delete: 20,
      strategy: 'pre',
      minimumBalance: 50,
    },
    premium: {
      read: 0, // 免费
      write: 0,
      delete: 0,
    },
    admin: {
      read: 0,
      write: 0,
      delete: 0,
    },
  });
}

/**
 * 创建高级 API 积分配置
 */
export function advancedApiCredits(): RouteCreditConfig {
  return new CreditConfigBuilder()
    .path('/api/analytics/*', {
      GET: {
        cost: async (context) => {
          const { startDate, endDate, groupBy } = context.req.query;
          
          let cost = 5;
          
          if (startDate && endDate) {
            const days = Math.ceil(
              (new Date(endDate).getTime() - new Date(startDate).getTime()) / (1000 * 60 * 60 * 24)
            );
            cost += Math.min(days * 0.5, 20);
          }
          
          if (groupBy) {
            cost += 10;
          }
          
          return Math.ceil(cost);
        },
        deductionStrategy: 'post',
        refundOnFailure: true,
      },
    })
    .path('/api/export/*', {
      GET: {
        cost: 25,
        deductionStrategy: 'pre',
        minimumBalance: 50,
        refundOnFailure: true,
      },
    })
    .build();
}
