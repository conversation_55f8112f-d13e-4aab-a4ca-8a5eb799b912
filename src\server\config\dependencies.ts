/**
 * 依赖管理配置
 * 处理可选依赖和框架特定的导入
 */

// 依赖可用性检查
export const DependencyChecker = {
  // 检查 Next.js 是否可用
  async checkNextJS(): Promise<boolean> {
    try {
      await import('next/server');
      return true;
    } catch {
      return false;
    }
  },

  // 检查 Hono 是否可用
  async checkHono(): Promise<boolean> {
    try {
      await import('hono');
      return true;
    } catch {
      return false;
    }
  },

  // 检查 Drizzle ORM 是否可用
  async checkDrizzle(): Promise<boolean> {
    try {
      await import('drizzle-orm');
      return true;
    } catch {
      return false;
    }
  },

  // 检查所有依赖
  async checkAll() {
    const [nextjs, hono, drizzle] = await Promise.all([
      this.checkNextJS(),
      this.checkHono(),
      this.checkDrizzle()
    ]);

    return {
      nextjs,
      hono,
      drizzle,
      summary: {
        available: [nextjs && 'Next.js', hono && 'Hono', drizzle && 'Drizzle'].filter(Boolean),
        missing: [!nextjs && 'Next.js', !hono && 'Hono', !drizzle && 'Drizzle'].filter(Boolean)
      }
    };
  }
};

// 安全的动态导入工具
export class SafeImporter {
  private static cache = new Map<string, any>();

  static async import<T = any>(moduleName: string, fallback?: T): Promise<T | null> {
    // 检查缓存
    if (this.cache.has(moduleName)) {
      return this.cache.get(moduleName);
    }

    try {
      const module = await import(moduleName);
      this.cache.set(moduleName, module);
      return module;
    } catch (error) {
      console.warn(`Failed to import ${moduleName}:`, error);
      const result = fallback || null;
      this.cache.set(moduleName, result);
      return result;
    }
  }

  static async importNextJS() {
    return this.import('next/server', {
      NextRequest: class MockNextRequest {},
      NextResponse: class MockNextResponse {
        static json(data: any, init?: any) {
          return new Response(JSON.stringify(data), {
            status: init?.status || 200,
            headers: { 'Content-Type': 'application/json', ...init?.headers }
          });
        }
      }
    });
  }

  static async importHono() {
    return this.import('hono', {
      Hono: class MockHono {
        get() { throw new Error('Hono not available'); }
        post() { throw new Error('Hono not available'); }
        put() { throw new Error('Hono not available'); }
        delete() { throw new Error('Hono not available'); }
      }
    });
  }
}

// 环境检测
export const Environment = {
  isNextJS(): boolean {
    return typeof process !== 'undefined' && 
           (process.env.NEXT_RUNTIME !== undefined || 
            typeof (globalThis as any).__NEXT_DATA__ !== 'undefined');
  },

  isBun(): boolean {
    return typeof Bun !== 'undefined';
  },

  isNode(): boolean {
    return typeof process !== 'undefined' && process.versions?.node !== undefined;
  },

  getRuntime(): 'nextjs' | 'bun' | 'node' | 'unknown' {
    if (this.isNextJS()) return 'nextjs';
    if (this.isBun()) return 'bun';
    if (this.isNode()) return 'node';
    return 'unknown';
  }
};

// 依赖注入配置
export interface DependencyConfig {
  runtime: 'nextjs' | 'hono' | 'auto';
  database: 'drizzle' | 'libsql' | 'auto';
  optional: {
    cache: boolean;
    auth: boolean;
    rateLimit: boolean;
  };
}

export const defaultDependencyConfig: DependencyConfig = {
  runtime: 'auto',
  database: 'auto',
  optional: {
    cache: true,
    auth: false,
    rateLimit: false
  }
};
