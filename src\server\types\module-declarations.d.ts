/**
 * 模块声明文件
 * 为可选依赖提供类型定义，避免编译错误
 */

// Next.js 模块声明
declare module 'next/server' {
  export interface NextRequest {
    method: string;
    url: string;
    headers: Headers;
    json(): Promise<any>;
    text(): Promise<string>;
    formData(): Promise<FormData>;
    clone(): NextRequest;
  }

  export class NextResponse {
    static json(data: any, init?: ResponseInit): Response;
    static redirect(url: string, status?: number): Response;
  }
}

// Hono 模块声明已移除
// 现在直接使用真实的 Hono 包类型，避免类型冲突
// 如果需要在没有 Hono 包的环境中使用，可以通过条件导入处理

// 可选依赖的全局类型
declare global {
  // 如果模块不存在，提供备用类型
  namespace NodeJS {
    interface ProcessEnv {
      NEXT_RUNTIME?: string;
      __NEXT_PRIVATE_PREBUNDLED_REACT?: string;
    }
  }
}
