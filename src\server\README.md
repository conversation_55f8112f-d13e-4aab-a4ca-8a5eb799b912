# API 构建器 - 简化架构

这是一个简化的 API 构建器设计，支持 Next.js 和 Hono 两种框架，通过不同的入口点来区分使用场景。

## 设计原则

1. **框架分离**: 每个框架有独立的构建器和入口点
2. **简单明确**: 避免复杂的适配器模式和运行时切换
3. **易于使用**: 开发者根据项目类型选择对应的入口点

## 项目结构

```
src/server/
├── builders/
│   ├── base-route-builder.ts    # 基础构建器（共享逻辑）
│   ├── route-builder.ts         # Next.js 专用构建器
│   ├── hono-route-builder.ts    # Hono 专用构建器
│   ├── next.ts                  # Next.js 项目入口
│   ├── hono.ts                  # Hono 项目入口
│   └── query-builder.ts         # 查询构建器（共享）
├── db/                          # 数据库相关
├── middleware/                  # 中间件
├── utils/                       # 工具函数
└── types.ts                     # 类型定义
```

## 使用方式

### Hono 项目

```typescript
// 从 Hono 专用入口导入
import { HonoApiRouteBuilder, users } from '@/server/builders/hono';
import { Hono } from 'hono';

const app = new Hono();

const userRoutes = new HonoApiRouteBuilder()
  .resource('users', users)
  .prefix('/api')
  .schema(userSchema)
  .filterable({ name: { schema: z.string() } })
  .build();

// 自动注册路由
userRoutes.register(app);
```

### Next.js 项目

```typescript
// 从 Next.js 专用入口导入
import { ApiRouteBuilder } from '@/server/builders/next';

const userRoutes = new ApiRouteBuilder()
  .resource('users', usersTable)
  .prefix('/api')
  .schema(userSchema)
  .build();

// 导出 Next.js API 处理器
export const GET = userRoutes.GET;
export const POST = userRoutes.POST;
```

## 依赖处理

### Next.js 依赖

- `next/server` 类型只在 Next.js 构建器中使用
- 使用 `type` 导入避免运行时依赖
- Next.js 项目需要安装 `next` 包

### Hono 依赖

- `hono` 类型只在 Hono 构建器中使用
- Hono 项目需要安装 `hono` 包

### 数据库依赖

- 支持 libSQL (Turso) 和 Drizzle ORM
- 数据库连接在 `src/server/db/` 中配置
- 可以根据项目需要选择不同的数据库适配器

## 优势

1. **简单直接**: 每个框架有专门的入口点，不需要运行时切换
2. **类型安全**: 每个构建器都有对应框架的完整类型支持
3. **易于维护**: 框架特定的代码分离，便于独立维护和更新
4. **灵活扩展**: 可以轻松添加新框架支持，只需创建新的构建器和入口点

## 迁移指南

### 从复杂适配器模式迁移

1. 删除 `adapters/` 目录
2. 删除 `universal-route-builder.ts`
3. 根据项目类型选择对应的入口点：
   - Hono 项目: `import from '@/server/builders/hono'`
   - Next.js 项目: `import from '@/server/builders/next'`

### 添加新框架支持

1. 创建框架专用的构建器 (如 `fastify-route-builder.ts`)
2. 创建框架专用的入口点 (如 `fastify.ts`)
3. 在入口点中导出框架相关的构建器和工具
