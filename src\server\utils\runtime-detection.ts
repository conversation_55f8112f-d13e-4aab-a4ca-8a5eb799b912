/**
 * 运行时检测和动态导入工具
 */

// 检查是否在 Next.js 环境中
export function isNextJSEnvironment(): boolean {
  try {
    // 检查 Next.js 特有的环境变量或全局对象
    return (
      typeof process !== 'undefined' &&
      (process.env.NEXT_RUNTIME !== undefined ||
       process.env.__NEXT_PRIVATE_PREBUNDLED_REACT !== undefined ||
       typeof (globalThis as any).__NEXT_DATA__ !== 'undefined')
    );
  } catch {
    return false;
  }
}

// 检查是否在 Bun 环境中
export function isBunEnvironment(): boolean {
  try {
    return typeof Bun !== 'undefined';
  } catch {
    return false;
  }
}

// 动态导入 Next.js 类型和工具
export async function importNextJS() {
  try {
    const nextServer = await import('next/server');
    return {
      NextRequest: nextServer.NextRequest,
      NextResponse: nextServer.NextResponse,
      available: true
    };
  } catch (error) {
    console.warn('Next.js not available:', error);
    return {
      NextRequest: null,
      NextResponse: null,
      available: false
    };
  }
}

// 动态导入 Hono
export async function importHono() {
  try {
    const hono = await import('hono');
    return {
      Hono: hono.Hono,
      Context: null, // Context 是类型，不是运行时值
      available: true
    };
  } catch (error) {
    console.warn('Hono not available:', error);
    return {
      Hono: null,
      Context: null,
      available: false
    };
  }
}

// 运行时适配器工厂
export class RuntimeAdapterFactory {
  private static instance: RuntimeAdapterFactory;
  
  static getInstance(): RuntimeAdapterFactory {
    if (!RuntimeAdapterFactory.instance) {
      RuntimeAdapterFactory.instance = new RuntimeAdapterFactory();
    }
    return RuntimeAdapterFactory.instance;
  }

  async createRequestHandler(type: 'next' | 'hono') {
    switch (type) {
      case 'next':
        const nextJS = await importNextJS();
        if (!nextJS.available) {
          throw new Error('Next.js is not available in this environment');
        }
        return this.createNextRequestHandler();
        
      case 'hono':
        const hono = await importHono();
        if (!hono.available) {
          throw new Error('Hono is not available in this environment');
        }
        return this.createHonoRequestHandler();
        
      default:
        throw new Error(`Unsupported runtime type: ${type}`);
    }
  }

  private createNextRequestHandler() {
    return (req: any, params?: any) => {
      // Next.js 请求处理逻辑
      return {
        method: req.method,
        url: req.url,
        headers: Object.fromEntries(req.headers.entries()),
        json: () => req.json(),
        text: () => req.text(),
        params: params || {}
      };
    };
  }

  private createHonoRequestHandler() {
    return (c: any) => {
      // Hono 请求处理逻辑
      return {
        method: c.req.method,
        url: c.req.url,
        headers: Object.fromEntries(c.req.header()),
        json: () => c.req.json(),
        text: () => c.req.text(),
        params: c.req.param()
      };
    };
  }
}
