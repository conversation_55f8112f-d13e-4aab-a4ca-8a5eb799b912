import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { prettyJSON } from 'hono/pretty-json';
import { serveStatic } from 'hono/bun';
import path from 'path';

// Import database initialization
import { initializeDatabase } from './config/database';

// Import file router system
import { createFileRouter } from './server/core/file-router';



// Create Hono app
const app = new Hono();

// Global middleware
app.use('*', logger());
app.use('*', cors({
  origin: ['http://127.0.0.1:5500'],
  allowHeaders: ['Content-Type', 'Authorization'],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  exposeHeaders: ['Content-Type', 'Authorization'],
  credentials: true,
  maxAge: 600,
}));
app.use('*', prettyJSON());

// Serve static files
app.use('/public/*', serveStatic({ root: './' }));

// Next.js 风格的文件路由系统
async function setupFileRoutes() {
  console.log('🔄 设置文件路由系统...');

  try {
    const fileRouter = await createFileRouter(
      app,
      path.join(process.cwd(), 'src/app/api'),
      {
        verbose: true,
        prefix: '/api', // 添加 /api 前缀
        excludePatterns: [
          '**/*.test.*',
          '**/*.spec.*',
          '**/.*',
          '**/_*',
          '**/node_modules/**'
        ]
      }
    );

    console.log('✅ 文件路由系统初始化完成');
    console.log('📊 已注册路由数量:', fileRouter.getRegisteredRoutes().length);

    return fileRouter;
  } catch (error) {
    console.error('❌ 文件路由系统初始化失败:', error);
    throw error;
  }
}


// Global error handler
app.onError((err, c) => {
  console.error('Global error:', err);
  return c.json({
    error: 'Internal Server Error',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  }, 500);
});

// 404 handler
app.notFound((c) => {
  return c.json({
    error: 'Not Found',
    message: 'The requested endpoint does not exist',
    availableEndpoints: [
      '/health',
      '/api/v1/services',
      '/api/v1/keys',
      '/api/v1/usage-logs'
    ]
  }, 404);
});

// Initialize and start server
const port = 4000; // 强制使用端口 4000

// 初始化应用
async function initializeApp() {
  try {
    console.log('🚀 初始化应用...');

    // Initialize database
    await initializeDatabase();

    // 设置文件路由系统
    await setupFileRoutes();

    console.log('✅ 应用初始化完成');
    console.log(`🌐 服务器将运行在 http://localhost:${port}`);
    console.log(`📖 API 文档: http://localhost:${port}/docs`);
    console.log(`🔍 健康检查: http://localhost:${port}/api/health`);
    console.log(`📁 文件路由示例: http://localhost:${port}/api/users`);

  } catch (error) {
    console.error('❌ 应用初始化失败:', error);
    process.exit(1);
  }
}

// 启动初始化
initializeApp();

export default {
  port,
  fetch: app.fetch,
};
