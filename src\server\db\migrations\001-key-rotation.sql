-- 密钥轮换服务数据库迁移
-- 创建时间: 2025-08-07

-- 创建服务表
CREATE TABLE IF NOT EXISTS services (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  config TEXT NOT NULL, -- JSON 格式的配置
  is_active INTEGER NOT NULL DEFAULT 1,
  created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建密钥表
CREATE TABLE IF NOT EXISTS keys (
  id TEXT PRIMARY KEY,
  service_id TEXT NOT NULL,
  key_value TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'active',
  usage_count INTEGER NOT NULL DEFAULT 0,
  max_usage INTEGER NOT NULL,
  priority REAL NOT NULL DEFAULT 1.0,
  created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  last_used_at TEXT,
  FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE
);

-- 创建使用日志表
CREATE TABLE IF NOT EXISTS usage_logs (
  id TEXT PRIMARY KEY,
  key_id TEXT NOT NULL,
  service_id TEXT NOT NULL,
  timestamp TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  metadata TEXT, -- JSON 格式的元数据
  FOREIGN KEY (key_id) REFERENCES keys(id) ON DELETE CASCADE,
  FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE
);

-- 创建性能关键索引
CREATE INDEX IF NOT EXISTS idx_keys_service_status_usage 
ON keys(service_id, status, usage_count);

CREATE INDEX IF NOT EXISTS idx_keys_last_used 
ON keys(last_used_at);

CREATE INDEX IF NOT EXISTS idx_usage_logs_service_time 
ON usage_logs(service_id, timestamp);

-- 创建服务名称索引
CREATE INDEX IF NOT EXISTS idx_services_name 
ON services(name);

-- 创建服务活跃状态索引
CREATE INDEX IF NOT EXISTS idx_services_active 
ON services(is_active);
