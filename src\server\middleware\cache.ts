import type { Middleware, MiddlewareContext, NextFunction } from "../types";
import { CacheManager, createCacheManager } from "../cache/cache-manager";
import { memoryStorage, kvStorage } from "../cache/storage";
import type { NextRequest } from "../types/runtime";

export interface CacheMiddlewareOptions {
  ttl?: number;
  cacheKey?: string;
  cacheManager?: CacheManager;
  enableStats?: boolean;
  backfill?: boolean;
  priority?: number;
  warmups?: {
    params?: Record<string, any>;
    resourceName: string;
  }[];
}

export const cacheMiddlewareId = "cache-middleware";

const cacheManager = createCacheManager([
  { name: "memory", storage: memoryStorage },
  { name: "kv", storage: kvStorage },
]);

export function createCacheMiddleware(
  options: CacheMiddlewareOptions = {}
): Middleware {
  if (options.warmups) {
    options.warmups.forEach((warmup) => {
      const req = new NextRequest(
        `/api/${warmup.resourceName}?${new URLSearchParams(
          warmup.params
        ).toString()}`
      );
    });
  }

  return async (context: MiddlewareContext, next: NextFunction) => {
    const { req } = context;
    const method = req.method;
    const resourceName = context.state.get("resourceName");

    // 只缓存 GET 请求
    if (method !== "GET") {
      // 对于修改操作，保持原有的缓存失效逻辑
      if (["POST", "PUT", "DELETE", "PATCH"].includes(method)) {
        await cacheManager.invalidateResource(resourceName);
      }
      await next();
      return;
    }

    try {
      // 尝试获取缓存
      const cachedResponse = await cacheManager.get(resourceName);
      if (cachedResponse) {
        const headers: Record<string, string> = {
          "Content-Type": "application/json",
          "X-Cache": "HIT",
        };

        // 添加缓存统计信息
        if (options.enableStats) {
          const stats = cacheManager.getStats();
          headers["X-Cache-Stats"] = JSON.stringify({
            hits: stats.hits,
            misses: stats.misses,
            size: stats.size,
            backfills: stats.backfills,
          });
        }

        context.response = new Response(JSON.stringify(cachedResponse), {
          headers,
        });
        return;
      }

      // 执行后续中间件
      await next();

      // 缓存成功的响应
      if (context.response?.ok) {
        const responseData = await context.response.clone().json();
        await cacheManager.set(resourceName, responseData, {
          ttl: options.ttl,
          backfill: options.backfill,
          priority: options.priority,
        });

        // 添加缓存统计信息到响应头
        if (options.enableStats) {
          const stats = cacheManager.getStats();
          const headers = new Headers(context.response.headers);
          headers.set("X-Cache", "MISS");
          headers.set(
            "X-Cache-Stats",
            JSON.stringify({
              hits: stats.hits,
              misses: stats.misses,
              size: stats.size,
              backfills: stats.backfills,
            })
          );
          context.response = new Response(context.response.body, {
            status: context.response.status,
            statusText: context.response.statusText,
            headers,
          });
        }
      }
    } catch (error) {
      console.error("Cache middleware error:", error);
      await next();
    }
  };
}

// 导出默认配置的缓存中间件实例
export const cacheMiddleware =
  createCacheMiddleware({
    ttl: 60 * 60, // 1小时
    enableStats: true,
    backfill: true, // 默认启用回填
    priority: 1, // 默认使用最高优先级存储
    // warmups,
  });
