
/**
 * Next.js 项目专用入口
 * 只导出 Next.js 相关的构建器和工具
 */

// 导出 Next.js 专用的路由构建器
export { ApiRouteBuilder } from './route-builder';

// 导出基类（如果需要扩展）
export { BaseApiRouteBuilder } from './base-route-builder';

// 导出查询构建器
export { QueryBuilder, createQueryBuilder } from './query-builder';

// 导出类型（排除 Hono 特定类型）
export * from '../types';

// 导出工具
export * from '../utils';

// 导出中间件
export * from '../middleware';

// 默认导出 Next.js 构建器
export { ApiRouteBuilder as default } from './route-builder';
