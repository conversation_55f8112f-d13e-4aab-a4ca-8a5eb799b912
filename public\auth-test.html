<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Better Auth 测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .user-info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .section {
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Better Auth 认证测试</h1>
        
        <!-- 用户状态显示 -->
        <div id="userStatus" class="user-info" style="display: none;">
            <h3>当前用户信息</h3>
            <div id="userInfo"></div>
            <button onclick="logout()">退出登录</button>
        </div>

        <!-- 注册表单 -->
        <div class="section">
            <h2>📝 用户注册</h2>
            <form id="signupForm">
                <div class="form-group">
                    <label for="signupName">姓名:</label>
                    <input type="text" id="signupName" required>
                </div>
                <div class="form-group">
                    <label for="signupEmail">邮箱:</label>
                    <input type="email" id="signupEmail" required>
                </div>
                <div class="form-group">
                    <label for="signupPassword">密码:</label>
                    <input type="password" id="signupPassword" required>
                </div>
                <button type="submit">注册</button>
            </form>
        </div>

        <!-- 登录表单 -->
        <div class="section">
            <h2>🔑 用户登录</h2>
            <form id="signinForm">
                <div class="form-group">
                    <label for="signinEmail">邮箱:</label>
                    <input type="email" id="signinEmail" required>
                </div>
                <div class="form-group">
                    <label for="signinPassword">密码:</label>
                    <input type="password" id="signinPassword" required>
                </div>
                <button type="submit">登录</button>
            </form>
        </div>

        <!-- 消息显示区域 -->
        <div id="messages"></div>

        <!-- 测试按钮 -->
        <div class="section">
            <h2>🧪 API 测试</h2>
            <button onclick="checkSession()">检查会话状态</button>
            <button onclick="testProtectedRoute()">测试受保护路由</button>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:4000/api/auth';
        
        // 显示消息
        function showMessage(message, type = 'success') {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = type;
            messageDiv.textContent = message;
            messagesDiv.appendChild(messageDiv);
            
            // 3秒后自动移除消息
            setTimeout(() => {
                messageDiv.remove();
            }, 3000);
        }

        // 更新用户状态显示
        function updateUserStatus(user) {
            const userStatus = document.getElementById('userStatus');
            const userInfo = document.getElementById('userInfo');
            
            if (user) {
                userInfo.innerHTML = `
                    <p><strong>ID:</strong> ${user.id}</p>
                    <p><strong>姓名:</strong> ${user.name}</p>
                    <p><strong>邮箱:</strong> ${user.email}</p>
                    <p><strong>邮箱验证:</strong> ${user.emailVerified ? '已验证' : '未验证'}</p>
                    <p><strong>创建时间:</strong> ${new Date(user.createdAt).toLocaleString()}</p>
                `;
                userStatus.style.display = 'block';
            } else {
                userStatus.style.display = 'none';
            }
        }

        // 注册
        document.getElementById('signupForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const name = document.getElementById('signupName').value;
            const email = document.getElementById('signupEmail').value;
            const password = document.getElementById('signupPassword').value;

            try {
                const response = await fetch(`${API_BASE}/sign-up/email`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify({ name, email, password })
                });

                const data = await response.json();
                
                if (response.ok) {
                    showMessage('注册成功！', 'success');
                    updateUserStatus(data.user);
                    document.getElementById('signupForm').reset();
                } else {
                    showMessage(`注册失败: ${data.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                showMessage(`注册失败: ${error.message}`, 'error');
            }
        });

        // 登录
        document.getElementById('signinForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('signinEmail').value;
            const password = document.getElementById('signinPassword').value;

            try {
                const response = await fetch(`${API_BASE}/sign-in/email`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify({ email, password })
                });

                const data = await response.json();
                
                if (response.ok) {
                    showMessage('登录成功！', 'success');
                    updateUserStatus(data.user);
                    document.getElementById('signinForm').reset();
                } else {
                    showMessage(`登录失败: ${data.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                showMessage(`登录失败: ${error.message}`, 'error');
            }
        });

        // 退出登录
        async function logout() {
            try {
                const response = await fetch(`${API_BASE}/sign-out`, {
                    method: 'POST',
                    credentials: 'include'
                });

                if (response.ok) {
                    showMessage('退出登录成功！', 'success');
                    updateUserStatus(null);
                } else {
                    showMessage('退出登录失败', 'error');
                }
            } catch (error) {
                showMessage(`退出登录失败: ${error.message}`, 'error');
            }
        }

        // 检查会话状态
        async function checkSession() {
            try {
                const response = await fetch(`${API_BASE}/get-session`, {
                    credentials: 'include'
                });

                const data = await response.json();
                
                if (response.ok && data.user) {
                    showMessage('会话有效', 'success');
                    updateUserStatus(data.user);
                } else {
                    showMessage('未登录或会话已过期', 'error');
                    updateUserStatus(null);
                }
            } catch (error) {
                showMessage(`检查会话失败: ${error.message}`, 'error');
            }
        }

        // 测试受保护路由
        async function testProtectedRoute() {
            try {
                const response = await fetch('/api/auto-users', {
                    credentials: 'include'
                });

                if (response.ok) {
                    const data = await response.json();
                    showMessage(`受保护路由访问成功，返回 ${data.data?.length || 0} 条数据`, 'success');
                } else {
                    showMessage('受保护路由访问失败，可能需要登录', 'error');
                }
            } catch (error) {
                showMessage(`测试受保护路由失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时检查会话状态
        window.addEventListener('load', () => {
            checkSession();
        });
    </script>
</body>
</html>
