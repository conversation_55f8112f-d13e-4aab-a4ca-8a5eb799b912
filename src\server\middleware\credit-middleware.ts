// 独立的积分中间件系统
import type { Middleware } from "../types";
import { ResponseUtils } from "../utils/response";
import { db } from "../../app/db";
import { user, userUsage } from "../../app/schemas/auth";
import { eq } from "drizzle-orm";

/**
 * 积分配置接口
 */
export interface CreditConfig {
  cost: number | ((context: any) => Promise<number>);
  deductionStrategy: 'pre' | 'post' | 'reserve';
  refundOnFailure?: boolean;
  minimumBalance?: number;
  skipForRoles?: string[];
  skipForUsers?: string[];
  enabled?: boolean;
}

/**
 * 方法级别的积分配置
 */
export interface MethodCreditConfig {
  GET?: CreditConfig;
  POST?: CreditConfig;
  PUT?: CreditConfig;
  DELETE?: CreditConfig;
  PATCH?: CreditConfig;
  [method: string]: CreditConfig | undefined;
}

/**
 * 路由级别的积分配置
 */
export interface RouteCreditConfig {
  // 全局配置（应用于所有方法）
  global?: Partial<CreditConfig>;
  
  // 方法特定配置
  methods?: MethodCreditConfig;
  
  // 路径特定配置
  paths?: {
    [path: string]: MethodCreditConfig;
  };
  
  // 动态配置函数
  dynamic?: (context: any) => Promise<CreditConfig | null>;
}

/**
 * 积分操作记录
 */
interface CreditTransaction {
  userId: string;
  operation: string;
  amount: number;
  balanceBefore: number;
  balanceAfter: number;
  timestamp: Date;
  metadata?: Record<string, any>;
}

/**
 * 积分服务类
 */
class CreditService {
  /**
   * 计算操作所需的积分
   */
  async calculateCredits(context: any, config: CreditConfig): Promise<number> {
    if (typeof config.cost === 'function') {
      return await config.cost(context);
    }
    return config.cost;
  }

  /**
   * 获取用户当前积分余额
   */
  async getUserCredits(userId: string): Promise<number> {
    const userRecord = await db
      .select({ credits: userUsage.credits })
      .from(userUsage)
      .where(eq(userUsage.userId, userId))
      .limit(1);
      
    return userRecord[0]?.credits || 0;
  }

  /**
   * 更新用户积分余额
   */
  async updateUserCredits(
    userId: string, 
    amount: number, 
    operation: string,
    metadata?: Record<string, any>
  ): Promise<{ balanceBefore: number; balanceAfter: number }> {
    const balanceBefore = await this.getUserCredits(userId);
    const balanceAfter = balanceBefore + amount;
    
    await db
      .update(userUsage)
      .set({ 
        credits: balanceAfter,
        updatedAt: new Date()
      })
      .where(eq(userUsage.userId, userId));
      
    // 记录交易
    await this.logCreditTransaction({
      userId,
      operation,
      amount,
      balanceBefore,
      balanceAfter,
      timestamp: new Date(),
      metadata,
    });
    
    return { balanceBefore, balanceAfter };
  }

  /**
   * 记录积分交易
   */
  private async logCreditTransaction(transaction: CreditTransaction): Promise<void> {
    // 这里可以记录到专门的交易日志表
    console.log('Credit Transaction:', transaction);
  }

  /**
   * 检查用户是否应该跳过积分检查
   */
  shouldSkipCredits(user: any, config: CreditConfig): boolean {
    if (config.skipForRoles?.includes(user.role)) {
      return true;
    }
    if (config.skipForUsers?.includes(user.id)) {
      return true;
    }
    return false;
  }
}

/**
 * 积分中间件工厂函数
 */
export function createCreditMiddleware(config: RouteCreditConfig): Middleware {
  const creditService = new CreditService();

  return async (context, next) => {
    try {
      // 检查是否启用积分系统
      if (config.global?.enabled === false) {
        await next();
        return;
      }

      const method = context.req.method;
      const path = new URL(context.req.url).pathname;

      // 解析积分配置
      let creditConfig = await resolveCreditConfig(config, context, method, path);
      
      if (!creditConfig || creditConfig.enabled === false) {
        await next();
        return;
      }

      // 获取用户信息
      const user = context.state.get("user");
      if (!user) {
        throw ResponseUtils.unauthorized("Authentication required for credit operations");
      }

      // 检查是否跳过积分检查
      if (creditService.shouldSkipCredits(user, creditConfig)) {
        await next();
        return;
      }

      // 计算所需积分
      const requiredCredits = await creditService.calculateCredits(context, creditConfig);
      const currentBalance = await creditService.getUserCredits(user.id);

      // 检查最低余额要求
      const minimumBalance = creditConfig.minimumBalance || 0;
      if (currentBalance < minimumBalance + requiredCredits) {
        throw ResponseUtils.forbidden(
          `Insufficient credits. Required: ${requiredCredits}, Available: ${currentBalance - minimumBalance}`
        );
      }

      // 执行积分操作
      await executeCreditOperation(
        creditService,
        user.id,
        requiredCredits,
        creditConfig,
        context,
        next
      );

    } catch (error) {
      if (error instanceof Response) {
        throw error;
      }
      throw ResponseUtils.internalError("Credit system error");
    }
  };
}

/**
 * 解析积分配置
 */
async function resolveCreditConfig(
  config: RouteCreditConfig,
  context: any,
  method: string,
  path: string
): Promise<CreditConfig | null> {
  // 1. 检查动态配置
  if (config.dynamic) {
    const dynamicConfig = await config.dynamic(context);
    if (dynamicConfig) {
      return mergeConfigs(config.global, dynamicConfig);
    }
  }

  // 2. 检查路径特定配置
  if (config.paths) {
    for (const [pathPattern, pathConfig] of Object.entries(config.paths)) {
      if (matchPath(path, pathPattern)) {
        const methodConfig = pathConfig[method as keyof MethodCreditConfig];
        if (methodConfig) {
          return mergeConfigs(config.global, methodConfig);
        }
      }
    }
  }

  // 3. 检查方法特定配置
  if (config.methods) {
    const methodConfig = config.methods[method as keyof MethodCreditConfig];
    if (methodConfig) {
      return mergeConfigs(config.global, methodConfig);
    }
  }

  return null;
}

/**
 * 执行积分操作
 */
async function executeCreditOperation(
  creditService: CreditService,
  userId: string,
  requiredCredits: number,
  config: CreditConfig,
  context: any,
  next: any
): Promise<void> {
  const operation = `${config.deductionStrategy}:${context.req.method}:${context.req.url}`;
  let creditReservation: { balanceBefore: number; balanceAfter: number } | null = null;

  // 根据扣费策略处理
  switch (config.deductionStrategy) {
    case 'pre':
    case 'reserve':
      creditReservation = await creditService.updateUserCredits(
        userId, 
        -requiredCredits, 
        operation
      );
      break;
  }

  // 执行实际操作
  let operationSuccess = false;
  try {
    await next();
    operationSuccess = true;
  } catch (error) {
    // 操作失败，根据配置决定是否退款
    if (creditReservation && config.refundOnFailure) {
      await creditService.updateUserCredits(
        userId, 
        requiredCredits, 
        `refund:${operation}`
      );
    }
    throw error;
  }

  // 操作成功后的处理
  const status = context.response?.status || 200;
  const isSuccess = status >= 200 && status < 300;

  if (config.deductionStrategy === 'post' && isSuccess) {
    await creditService.updateUserCredits(
      userId, 
      -requiredCredits, 
      operation
    );
  } else if (config.deductionStrategy === 'reserve' && !isSuccess && config.refundOnFailure) {
    await creditService.updateUserCredits(
      userId, 
      requiredCredits, 
      `refund:${operation}`
    );
  }

  // 在响应头中添加积分信息
  const finalBalance = await creditService.getUserCredits(userId);
  context.response = new Response(context.response?.body, {
    ...context.response,
    headers: {
      ...context.response?.headers,
      'X-Credits-Remaining': finalBalance.toString(),
      'X-Credits-Used': requiredCredits.toString(),
    },
  });
}

/**
 * 合并配置对象
 */
function mergeConfigs(global?: Partial<CreditConfig>, specific?: CreditConfig): CreditConfig {
  return {
    cost: 1,
    deductionStrategy: 'post',
    refundOnFailure: true,
    enabled: true,
    ...global,
    ...specific,
  };
}

/**
 * 路径匹配函数
 */
function matchPath(path: string, pattern: string): boolean {
  // 简单的通配符匹配，可以扩展为更复杂的模式
  if (pattern.includes('*')) {
    const regex = new RegExp(pattern.replace(/\*/g, '.*'));
    return regex.test(path);
  }
  return path === pattern;
}
