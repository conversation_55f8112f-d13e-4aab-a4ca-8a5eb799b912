// 通用服务层中间件导出
// 注意：业务特定的中间件应该放在 src/app/middleware/ 中

// 通用中间件
export { createLoggerMiddleware } from "./logger";
export { createRateLimitMiddleware } from "./rate-limit";
export { createCacheMiddleware } from "./cache";

// Generic auth middleware (consolidated from app layer)
export {
  createAuthMiddleware,
  requireAuth,
  authByMethod,
  publicEndpoint,
  type AuthMiddlewareConfig
} from "./auth";

// Generic credit middleware (consolidated from app layer)
export {
  createCreditMiddleware,
  requireCredits,
  creditsByMethod,
  standardCredits,
  freeEndpoint,
  type CreditMiddlewareConfig,
  type MethodCreditConfig,
  type CreditCost,
  type DeductionStrategy
} from "./credits";

// Auth helpers & middleware (convention-first)
export { honoAuthSessionMiddleware, requireAuth as honoRequireAuth, requireRoles, requireScopes, withAuth } from "../auth/middleware";