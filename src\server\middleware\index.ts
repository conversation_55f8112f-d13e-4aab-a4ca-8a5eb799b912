// 通用服务层中间件导出
// 注意：业务特定的中间件应该放在 src/app/middleware/ 中

// 通用中间件
export { createLoggerMiddleware } from "./logger";
export { createRateLimitMiddleware } from "./rate-limit";
export { createCacheMiddleware } from "./cache";

// Auth helpers & middleware (convention-first)
export { honoAuthSessionMiddleware, requireAuth, requireRoles, requireScopes, withAuth } from "../auth/middleware";

// 业务特定中间件已移动到 src/app/middleware/
// export { createAuthMiddleware } from "./auth";           // 移动到 src/app/middleware/auth.ts
// export { createApiKeyAuthMiddleware } from "./apikey";   // 移动到 src/app/middleware/apikey.ts