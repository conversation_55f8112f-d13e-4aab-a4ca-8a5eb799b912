

export async function createTestRequest({
    method = 'GET',
    path,
    data,
    query = {},
    headers = {}
}: {
    method?: string;
    path: string;
    data?: any;
    query?: Record<string, string>;
    headers?: Record<string, string>;
}): Promise<Response> {
    const url = new URL(`http://localhost:3000${path}`);

    // 添加查询参数
    Object.entries(query).forEach(([key, value]) => {
        url.searchParams.append(key, value);
    });

    return fetch(url, {
        method,
        headers: {
            'Content-Type': 'application/json',
            ...headers
        },
        body: data ? JSON.stringify(data) : undefined
    });
}
