import type { Middleware } from "../types";
import { ResponseUtils } from "../utils/response";
import { RESPONSE_CODES } from "../constants";
import QuickLRU from "quick-lru";

interface RateLimitConfig {
  windowMs: number; // 时间窗口，单位毫秒
  max: number; // 在时间窗口内允许的最大请求数
}

interface RateLimitOptions {
  GET?: RateLimitConfig;
  POST?: RateLimitConfig;
  PUT?: RateLimitConfig;
  DELETE?: RateLimitConfig;
  skipFailedRequests?: boolean; // 是否跳过失败的请求
  skipSuccessfulRequests?: boolean; // 是否跳过成功的请求
  identifierResolver?: (context: any) => Promise<string> | string; // 自定义标识符解析器
  maxSize?: number; // LRU 缓存最大容量
}

// HTTP状态码
const HTTP_STATUS = { MULTIPLE_CHOICES: 300 } as const;

// 使用 QuickLRU 存储请求记录，默认最大存储10000条记录
const requestStore = new QuickLRU<string, { count: number; resetTime: number }>(
  {
    maxSize: 10000,
  }
);

/**
 * 获取请求的唯一标识符
 * 优先级：自定义解析器 > 用户ID > IP地址 > 匿名
 */
async function getRequestIdentifier(
  context: any,
  options: RateLimitOptions
): Promise<string> {
  try {
    // 使用自定义解析器
    if (options.identifierResolver) {
      const customIdentifier = await options.identifierResolver(context);
      if (customIdentifier) {
        return customIdentifier;
      }
    }

    // 默认标识符解析逻辑
    return (
      context.state.get("user")?.id ||
      context.req.headers.get("x-forwarded-for") ||
      context.req.headers.get("x-real-ip") ||
      "anonymous"
    );
  } catch (error) {
    console.error("Error resolving rate limit identifier:", error);
    return "anonymous";
  }
}

export function createRateLimitMiddleware(
  options: RateLimitOptions = {}
): Middleware {
  const defaultConfig: RateLimitConfig = {
    windowMs: 60000, // 默认1分钟
    max: 60, // 默认每分钟60次请求
  };

  // 如果提供了自定义maxSize，则重新创建requestStore
  if (options.maxSize && options.maxSize !== requestStore.maxSize) {
    requestStore.clear();
    const newStore = new QuickLRU<string, { count: number; resetTime: number }>(
      {
        maxSize: options.maxSize,
      }
    );
    Object.entries(requestStore).forEach(([key, value]) => {
      newStore.set(key, value);
    });
  }

  return async (context, next) => {
    try {
      const method = context.req.method as keyof RateLimitOptions;
      const methodConfig = options[method] as RateLimitConfig | undefined;
      const config: RateLimitConfig = methodConfig || defaultConfig;

      // 获取用户标识
      const identifier = await getRequestIdentifier(context, options);
      const key = `${identifier}:${method}`;
      const now = Date.now();

      let record = requestStore.get(key);

      // 如果记录不存在或已过期，创建新记录
      if (!record || now >= record.resetTime) {
        record = {
          count: 0,
          resetTime: now + config.windowMs,
        };
        requestStore.set(key, record);
      }

      // 检查是否超过限制
      if (record.count >= config.max) {
        const retryAfter = Math.ceil((record.resetTime - now) / 1000);
        throw ResponseUtils.tooManyRequests(
          `Rate limit exceeded for ${method}. Try again in ${retryAfter} seconds.`,
          { retryAfter }
        );
      }

      // 增加计数
      record.count++;

      // 设置响应头
      context.response = new Response(null, {
        headers: {
          "X-RateLimit-Limit": config.max.toString(),
          "X-RateLimit-Remaining": (config.max - record.count).toString(),
          "X-RateLimit-Reset": Math.ceil(record.resetTime / 1000).toString(),
        },
      });

      await next();

      // 根据配置决定是否计算成功/失败的请求
      const status = context.response?.status || RESPONSE_CODES.INTERNAL_ERROR;
      if (
        (options.skipSuccessfulRequests &&
          status >= RESPONSE_CODES.SUCCESS &&
          status < HTTP_STATUS.MULTIPLE_CHOICES) ||
        (options.skipFailedRequests && status >= RESPONSE_CODES.BAD_REQUEST)
      ) {
        record.count--;
      }
    } catch (error) {
      if (error instanceof Response) {
        throw error;
      }
      throw ResponseUtils.internalError("Rate limit middleware error");
    }
  };
}
