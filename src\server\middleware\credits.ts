import type { Middleware, MiddlewareContext, NextFunction, HttpMethod } from "../types";
import { ResponseUtils } from "../utils/response";
import { db } from "../../app/db";
import { userUsage } from "../../app/schemas/auth";
import { eq } from "drizzle-orm";

/**
 * Credit cost configuration - can be static number or dynamic function
 */
export type CreditCost = number | ((context: MiddlewareContext) => Promise<number> | number);

/**
 * Credit deduction strategy
 */
export type DeductionStrategy = 'pre' | 'post' | 'reserve';

/**
 * Method-specific credit configuration
 */
export interface MethodCreditConfig {
  cost: CreditCost;
  deductionStrategy?: DeductionStrategy;
  refundOnFailure?: boolean;
  minimumBalance?: number;
  skipForRoles?: string[];
  skipForUsers?: string[];
}

/**
 * Generic credit middleware configuration
 */
export interface CreditMiddlewareConfig {
  // Global settings
  enabled?: boolean;
  defaultCost?: CreditCost;
  defaultStrategy?: DeductionStrategy;
  defaultMinimumBalance?: number;

  // Method-specific configurations
  methods?: {
    [key in HttpMethod]?: MethodCreditConfig;
  };

  // Global skip conditions
  skipForRoles?: string[];
  skipForUsers?: string[];

  // Response handling
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;

  // Custom functions
  getUserCredits?: (userId: string) => Promise<number>;
  updateUserCredits?: (userId: string, amount: number, operation: string) => Promise<void>;
  shouldSkip?: (user: any, context: MiddlewareContext) => boolean | Promise<boolean>;
}

/**
 * Credit service for database operations
 */
class CreditService {
  constructor(private config: CreditMiddlewareConfig) {}

  async getUserCredits(userId: string): Promise<number> {
    if (this.config.getUserCredits) {
      return this.config.getUserCredits(userId);
    }

    const userRecord = await db
      .select({ credits: userUsage.credits })
      .from(userUsage)
      .where(eq(userUsage.userId, userId))
      .limit(1);

    return userRecord[0]?.credits || 0;
  }

  async updateUserCredits(userId: string, amount: number, operation: string): Promise<void> {
    if (this.config.updateUserCredits) {
      return this.config.updateUserCredits(userId, amount, operation);
    }

    const currentCredits = await this.getUserCredits(userId);
    const newCredits = currentCredits + amount;

    await db
      .update(userUsage)
      .set({
        credits: newCredits,
        updatedAt: new Date()
      })
      .where(eq(userUsage.userId, userId));
  }

  async calculateCost(cost: CreditCost, context: MiddlewareContext): Promise<number> {
    if (typeof cost === 'function') {
      return await cost(context);
    }
    return cost;
  }

  shouldSkipUser(user: any, config: MethodCreditConfig): boolean {
    // Check role-based skip
    if (config.skipForRoles?.includes(user.role)) {
      return true;
    }

    // Check user-based skip
    if (config.skipForUsers?.includes(user.id)) {
      return true;
    }

    // Check global role-based skip
    if (this.config.skipForRoles?.includes(user.role)) {
      return true;
    }

    // Check global user-based skip
    if (this.config.skipForUsers?.includes(user.id)) {
      return true;
    }

    return false;
  }
}

/**
 * Generic credit middleware factory
 * Consolidates functionality from both app and server credit implementations
 */
export function createCreditMiddleware(config: CreditMiddlewareConfig = {}): Middleware {
  const creditService = new CreditService(config);

  return async (context: MiddlewareContext, next: NextFunction): Promise<void> => {
    try {
      // Check if credit system is disabled
      if (config.enabled === false) {
        await next();
        return;
      }

      const method = context.req?.method || context.method || 'GET';
      const methodConfig = config.methods?.[method as HttpMethod];

      // Skip if no configuration for this method and no default cost
      if (!methodConfig && !config.defaultCost) {
        await next();
        return;
      }

      // Get user from context (should be set by auth middleware)
      const user = context.state.get("user");
      if (!user) {
        throw ResponseUtils.unauthorized("Authentication required for credit operations");
      }

      // Check custom skip condition
      if (config.shouldSkip && await config.shouldSkip(user, context)) {
        await next();
        return;
      }

      // Check if user should be skipped
      if (methodConfig && creditService.shouldSkipUser(user, methodConfig)) {
        await next();
        return;
      }

      // Determine cost and configuration
      const cost = methodConfig?.cost ?? config.defaultCost;
      if (!cost) {
        await next();
        return;
      }

      const requiredCredits = await creditService.calculateCost(cost, context);
      const deductionStrategy = methodConfig?.deductionStrategy ?? config.defaultStrategy ?? 'post';
      const minimumBalance = methodConfig?.minimumBalance ?? config.defaultMinimumBalance ?? 0;
      const refundOnFailure = methodConfig?.refundOnFailure ?? true;

      // Check current balance
      const currentBalance = await creditService.getUserCredits(user.id);
      if (currentBalance < minimumBalance + requiredCredits) {
        throw ResponseUtils.forbidden(
          `Insufficient credits. Required: ${requiredCredits}, Available: ${currentBalance - minimumBalance}`
        );
      }

      // Handle pre-deduction or reserve strategy
      let deducted = false;
      if (deductionStrategy === 'pre' || deductionStrategy === 'reserve') {
        await creditService.updateUserCredits(
          user.id,
          -requiredCredits,
          `${deductionStrategy}:${method}:${context.req?.url || 'unknown'}`
        );
        deducted = true;
      }

      // Execute the request
      let requestSuccessful = false;
      try {
        await next();
        requestSuccessful = true;
      } catch (error) {
        // Handle request failure
        if (deducted && refundOnFailure) {
          await creditService.updateUserCredits(
            user.id,
            requiredCredits,
            `refund:${method}:${context.req?.url || 'unknown'}`
          );
        }
        throw error;
      }

      // Handle post-deduction
      const status = context.response?.status || 200;
      const isSuccess = status >= 200 && status < 300;

      if (deductionStrategy === 'post' && isSuccess) {
        await creditService.updateUserCredits(
          user.id,
          -requiredCredits,
          `post:${method}:${context.req?.url || 'unknown'}`
        );
      } else if (deductionStrategy === 'reserve' && !isSuccess && refundOnFailure) {
        await creditService.updateUserCredits(
          user.id,
          requiredCredits,
          `refund:${method}:${context.req?.url || 'unknown'}`
        );
      }

      // Handle skip conditions based on response
      const shouldSkipDeduction = (
        (config.skipSuccessfulRequests && isSuccess) ||
        (config.skipFailedRequests && !isSuccess)
      );

      if (shouldSkipDeduction && deducted && deductionStrategy !== 'reserve') {
        await creditService.updateUserCredits(
          user.id,
          requiredCredits,
          `skip:${method}:${context.req?.url || 'unknown'}`
        );
      }

      // Add credit info to response headers
      const finalBalance = await creditService.getUserCredits(user.id);
      if (context.response) {
        const headers = new Headers(context.response.headers);
        headers.set('X-Credits-Remaining', finalBalance.toString());
        headers.set('X-Credits-Used', requiredCredits.toString());

        context.response = new Response(context.response.body, {
          status: context.response.status,
          statusText: context.response.statusText,
          headers
        });
      }

    } catch (error) {
      if (error instanceof Response) {
        throw error;
      }
      console.error('Credit middleware error:', error);
      throw ResponseUtils.internalError("Credit system error");
    }
  };
}

/**
 * Convenience function for simple credit requirements
 */
export function requireCredits(cost: CreditCost, strategy: DeductionStrategy = 'post'): Middleware {
  return createCreditMiddleware({
    defaultCost: cost,
    defaultStrategy: strategy
  });
}

/**
 * Convenience function for method-specific credit requirements
 */
export function creditsByMethod(methodConfigs: CreditMiddlewareConfig['methods']): Middleware {
  return createCreditMiddleware({
    methods: methodConfigs
  });
}

/**
 * Convenience function for standard CRUD credit costs
 */
export function standardCredits(options: {
  read?: number;
  write?: number;
  delete?: number;
  strategy?: DeductionStrategy;
} = {}): Middleware {
  const { read = 1, write = 5, delete: del = 10, strategy = 'post' } = options;

  return createCreditMiddleware({
    methods: {
      GET: { cost: read, deductionStrategy: strategy },
      POST: { cost: write, deductionStrategy: strategy, refundOnFailure: true },
      PUT: { cost: write, deductionStrategy: strategy, refundOnFailure: true },
      PATCH: { cost: write, deductionStrategy: strategy, refundOnFailure: true },
      DELETE: { cost: del, deductionStrategy: 'pre', refundOnFailure: true }
    }
  });
}

/**
 * Convenience function for free endpoints (no credits required)
 */
export function freeEndpoint(): Middleware {
  return createCreditMiddleware({
    enabled: false
  });
}