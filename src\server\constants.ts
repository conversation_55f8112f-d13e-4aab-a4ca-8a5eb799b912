// Pagination constants
export const PAGINATION = {
  defaultLimit: 10,
  maxLimit: 10000,
  defaultPage: 1,
  pageSizeOptions: [10, 20, 50, 100],
} as const;

// Cache TTL constants (in seconds)
export const CACHE_TTL = {
  SHORT: 60, // 1 minute
  MEDIUM: 300, // 5 minutes
  LONG: 3600, // 1 hour
  DAY: 86400, // 24 hours
} as const;

// SQL operators
export const SQL_OPERATORS = {
  LIKE: "LIKE",
  ILIKE: "ILIKE",
  EQUALS: "=",
  IN: "IN",
  GT: ">",
  GTE: ">=",
  LT: "<",
  LTE: "<=",
  NOT_EQUALS: "!=",
} as const;

// Response codes
export const RESPONSE_CODES = {
  SUCCESS: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  INTERNAL_ERROR: 500,
} as const;
