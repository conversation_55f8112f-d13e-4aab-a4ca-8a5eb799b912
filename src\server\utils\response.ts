import { RESPONSE_CODES } from "../constants";
import type { ApiResponse, ApiErrorResponse } from "../types";

// HTTP状态码扩展
const HTTP_STATUS = { TOO_MANY_REQUESTS: 429 } as const;

// 通用响应创建函数，避免直接依赖框架
function createJsonResponse(data: any, options: { status?: number; headers?: Record<string, string> } = {}): Response {
  const { status = 200, headers = {} } = options;

  return new Response(JSON.stringify(data), {
    status,
    headers: {
      'Content-Type': 'application/json',
      ...CORS_HEADERS,
      ...headers
    }
  });
}

// CORS 头部配置
const CORS_HEADERS = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type, Authorization",
};

export class ResponseUtils {
  static success<T>(data: T, message?: string): Response {
    const response: ApiResponse<T> = {
      code: RESPONSE_CODES.SUCCESS,
      success: true,
      data,
      message,
    };
    return createJsonResponse(response);
  }

  static created<T>(data: T, message?: string): Response {
    const response: ApiResponse<T> = {
      code: RESPONSE_CODES.CREATED,
      success: true,
      data,
      message,
    };
    return createJsonResponse(response, { status: RESPONSE_CODES.CREATED });
  }

  static error(code: number, message: string, details?: any): Response {
    const response: ApiErrorResponse = {
      code,
      message,
      details,
    };
    return createJsonResponse(response, { status: code });
  }

  static badRequest(message: string, details?: any): Response {
    return this.error(RESPONSE_CODES.BAD_REQUEST, message, details);
  }

  static unauthorized(message: string = "Unauthorized"): Response {
    return this.error(RESPONSE_CODES.UNAUTHORIZED, message);
  }

  static forbidden(message: string = "Forbidden"): Response {
    return this.error(RESPONSE_CODES.FORBIDDEN, message);
  }

  static notFound(message: string = "Not found"): Response {
    return this.error(RESPONSE_CODES.NOT_FOUND, message);
  }

  static methodNotAllowed(message: string = "Method not allowed"): Response {
    return this.error(RESPONSE_CODES.METHOD_NOT_ALLOWED, message);
  }

  static internalError(message: string = "Internal server error"): Response {
    return this.error(RESPONSE_CODES.INTERNAL_ERROR, message);
  }

  static tooManyRequests(
    message: string = "Too many requests",
    details?: any
  ): Response {
    return this.error(HTTP_STATUS.TOO_MANY_REQUESTS, message, details);
  }
}
