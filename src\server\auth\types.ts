// Auth configuration types used by both API Builder and custom routes
import type { HttpMethod } from "../types";

export type AuthStrategy = "auto" | "session" | "bearer" | "none";

export interface RouteRule {
  required?: boolean; // whether auth is required
  roles?: string[];   // required roles
  scopes?: string[];  // required scopes/permissions
  // custom user check
  customCheck?: (user: any, ctx: { params: Record<string,string>; method: HttpMethod; path: string }) => boolean | Promise<boolean>;
}

export interface AuthConfig {
  strategy?: AuthStrategy; // default: auto (cookie or bearer)
  globalAuth?: RouteRule;  // fallback rule when route rule not provided
  routes?: Partial<Record<HttpMethod, RouteRule>>; // method-level overrides
  injectUser?: boolean; // default true: inject {user,session}
  onUnauthorized?: (reason: string) => Response; // optional custom response
  onForbidden?: (reason: string) => Response;    // optional custom response
}

export interface AuthCheckInput {
  method: HttpMethod;
  path: string;
  headers: Headers;
  resourceName?: string;
  config?: AuthConfig;
  params?: Record<string,string>;
}

export interface AuthCheckResult {
  authorized: boolean;
  user?: any;
  session?: any;
  reason?: string;
  response?: Response; // if a prebuilt response should be returned
}

