import type { Middleware, MiddlewareContext, NextFunction, HttpMethod } from "../types";
import { auth } from "../auth";
import { ResponseUtils } from "../utils/response";

/**
 * Generic authentication configuration interface
 */
export interface AuthMiddlewareConfig {
  // Global authentication settings
  required?: boolean;
  roles?: string[];

  // Method-specific authentication rules
  methods?: {
    [key in HttpMethod]?: {
      required?: boolean;
      roles?: string[];
      customValidator?: (user: any, context: MiddlewareContext) => boolean | Promise<boolean>;
    };
  };

  // Custom validation function for all requests
  customValidator?: (user: any, context: MiddlewareContext) => boolean | Promise<boolean>;

  // Skip authentication for specific conditions
  skipIf?: (context: MiddlewareContext) => boolean | Promise<boolean>;

  // Custom user data enrichment
  enrichUser?: (user: any, context: MiddlewareContext) => Promise<any>;

  // Error handling customization
  onUnauthorized?: (reason: string) => Response;
  onForbidden?: (reason: string) => Response;
}

/**
 * Generic authentication middleware factory
 * Consolidates functionality from both app and server auth implementations
 */
export function createAuthMiddleware(config: AuthMiddlewareConfig = {}): Middleware {
  return async (context: MiddlewareContext, next: NextFunction): Promise<void> => {
    try {
      // Check if authentication should be skipped
      if (config.skipIf && await config.skipIf(context)) {
        await next();
        return;
      }

      const method = context.req?.method || context.method || 'GET';
      const methodConfig = config.methods?.[method as HttpMethod];

      // Determine if authentication is required
      const isRequired = methodConfig?.required ?? config.required ?? true;

      if (!isRequired) {
        await next();
        return;
      }

      // Get session from better-auth
      const session = await auth.api.getSession({
        headers: context.req?.headers || context.headers || new Headers()
      });

      if (!session?.user) {
        const error = config.onUnauthorized
          ? config.onUnauthorized("Authentication required")
          : ResponseUtils.unauthorized("Authentication required");
        throw error;
      }

      let user = session.user;

      // Enrich user data if configured
      if (config.enrichUser) {
        user = await config.enrichUser(user, context);
      }

      // Check global roles
      if (config.roles && config.roles.length > 0) {
        const userRoles = (user as any)?.roles || [];
        if (!Array.isArray(userRoles) || !config.roles.some(role => userRoles.includes(role))) {
          const error = config.onForbidden
            ? config.onForbidden("Insufficient global roles")
            : ResponseUtils.forbidden("Insufficient global roles");
          throw error;
        }
      }

      // Check method-specific roles
      if (methodConfig?.roles && methodConfig.roles.length > 0) {
        const userRoles = (user as any)?.roles || [];
        if (!Array.isArray(userRoles) || !methodConfig.roles.some(role => userRoles.includes(role))) {
          const error = config.onForbidden
            ? config.onForbidden(`Insufficient roles for ${method} request`)
            : ResponseUtils.forbidden(`Insufficient roles for ${method} request`);
          throw error;
        }
      }

      // Run custom validation
      if (config.customValidator) {
        const isValid = await config.customValidator(user, context);
        if (!isValid) {
          const error = config.onForbidden
            ? config.onForbidden("Custom validation failed")
            : ResponseUtils.forbidden("Custom validation failed");
          throw error;
        }
      }

      // Run method-specific custom validation
      if (methodConfig?.customValidator) {
        const isValid = await methodConfig.customValidator(user, context);
        if (!isValid) {
          const error = config.onForbidden
            ? config.onForbidden(`Custom validation failed for ${method} request`)
            : ResponseUtils.forbidden(`Custom validation failed for ${method} request`);
          throw error;
        }
      }

      // Inject user and session into context
      context.state.set("user", user);
      context.state.set("session", session.session);
      context.state.set("auth", { user, session: session.session });

      await next();

    } catch (error) {
      if (error instanceof Response) {
        throw error;
      }
      console.error('Authentication middleware error:', error);
      throw ResponseUtils.internalError("Authentication error");
    }
  };
}

/**
 * Convenience function for simple authentication requirements
 */
export function requireAuth(roles?: string[]): Middleware {
  return createAuthMiddleware({
    required: true,
    roles: roles
  });
}

/**
 * Convenience function for method-specific authentication
 */
export function authByMethod(methodRules: AuthMiddlewareConfig['methods']): Middleware {
  return createAuthMiddleware({
    methods: methodRules
  });
}

/**
 * Convenience function for public endpoints (no auth required)
 */
export function publicEndpoint(): Middleware {
  return createAuthMiddleware({
    required: false
  });
}