import { Hono } from 'hono';
import type { Context } from 'hono';
import path from 'path';

/**
 * 路由处理器类型定义
 * 支持 Next.js 风格的 HTTP 方法导出
 */
export interface RouteHandlers {
  GET?: (c: Context) => Promise<Response> | Response;
  POST?: (c: Context) => Promise<Response> | Response;
  PUT?: (c: Context) => Promise<Response> | Response;
  DELETE?: (c: Context) => Promise<Response> | Response;
  PATCH?: (c: Context) => Promise<Response> | Response;
  OPTIONS?: (c: Context) => Promise<Response> | Response;
  HEAD?: (c: Context) => Promise<Response> | Response;
}

/**
 * 路由模块接口
 * 支持多种导出方式
 */
export interface RouteModule extends RouteHandlers {
  // 支持中间件配置
  middleware?: any[];
  // 支持动态路由配置
  generateStaticParams?: () => Promise<any[]> | any[];
}

/**
 * 文件路由发现选项
 */
export interface FileRouterOptions {
  routesDir: string;
  excludePatterns?: string[];
  includePatterns?: string[];
  verbose?: boolean;
  prefix?: string; // 路由前缀，例如 '/api'
}

/**
 * 注册的路由信息
 */
export interface RegisteredRoute {
  filePath: string;
  routePath: string;
  methods: string[];
  module: RouteModule;
}

/**
 * Next.js 风格的文件路由系统
 * 支持自动发现和注册基于文件系统的路由
 */
export class FileBasedRouter {
  private app: Hono;
  private options: FileRouterOptions;
  private registeredRoutes: RegisteredRoute[] = [];

  constructor(app: Hono, options: FileRouterOptions) {
    this.app = app;
    this.options = {
      excludePatterns: [
        '**/*.test.*',
        '**/*.spec.*',
        '**/.*',
        '**/_*',
        '**/node_modules/**'
      ],
      includePatterns: ['**/route.{ts,js}'],
      verbose: false,
      ...options
    };
  }

  /**
   * 自动发现并注册所有文件路由
   */
  async registerFileRoutes(): Promise<void> {
    try {
      if (this.options.verbose) {
        console.log('🔄 开始文件路由发现...');
      }

      const routeFiles = await this.discoverRouteFiles();
      
      if (this.options.verbose) {
        console.log(`📁 发现 ${routeFiles.length} 个路由文件`);
      }

      for (const filePath of routeFiles) {
        await this.registerRouteFile(filePath);
      }

      if (this.options.verbose) {
        console.log(`✅ 成功注册 ${this.registeredRoutes.length} 个文件路由`);
        this.printRegisteredRoutes();
      }

    } catch (error) {
      console.error('❌ 文件路由注册失败:', error);
      throw error;
    }
  }

  /**
   * 发现路由文件
   */
  private async discoverRouteFiles(): Promise<string[]> {
    const fs = await import('fs');
    const path = await import('path');

    const files: string[] = [];

    // 递归搜索路由文件
    const searchDirectory = async (dir: string): Promise<void> => {
      try {
        const entries = await fs.promises.readdir(dir, { withFileTypes: true });

        for (const entry of entries) {
          const fullPath = path.join(dir, entry.name);

          if (entry.isDirectory()) {
            // 检查是否应该排除此目录
            const shouldExclude = this.options.excludePatterns?.some(pattern => {
              const regex = new RegExp(pattern.replace(/\*\*/g, '.*').replace(/\*/g, '[^/]*'));
              return regex.test(entry.name);
            });

            if (!shouldExclude) {
              await searchDirectory(fullPath);
            }
          } else if (entry.isFile()) {
            // 检查文件是否匹配包含模式
            const shouldInclude = this.options.includePatterns?.some(pattern => {
              // 简化的模式匹配：支持 route.ts 和 route.js
              if (pattern === '**/route.{ts,js}') {
                return entry.name === 'route.ts' || entry.name === 'route.js';
              }
              // 其他模式的通用匹配
              const regex = new RegExp(pattern.replace(/\*\*/g, '.*').replace(/\*/g, '[^/]*').replace(/\{([^}]+)\}/g, '($1)'));
              return regex.test(entry.name);
            });

            // 检查文件是否应该排除
            const shouldExclude = this.options.excludePatterns?.some(pattern => {
              const regex = new RegExp(pattern.replace(/\*\*/g, '.*').replace(/\*/g, '[^/]*'));
              return regex.test(entry.name);
            });

            if (shouldInclude && !shouldExclude) {
              files.push(fullPath);
              if (this.options.verbose) {
                console.log(`📄 找到路由文件: ${fullPath}`);
              }
            }
          }
        }
      } catch (error) {
        // 忽略无法访问的目录
        if (this.options.verbose) {
          console.warn(`⚠️  无法访问目录: ${dir}`, error);
        }
      }
    };

    // 检查根目录是否存在
    try {
      await fs.promises.access(this.options.routesDir);
      await searchDirectory(this.options.routesDir);
    } catch (error) {
      if (this.options.verbose) {
        console.warn(`⚠️  路由目录不存在: ${this.options.routesDir}`);
      }
    }

    // 去重并排序
    return [...new Set(files)].sort();
  }

  /**
   * 注册单个路由文件
   */
  private async registerRouteFile(filePath: string): Promise<void> {
    try {
      // 动态导入路由模块
      const module = await import(filePath) as RouteModule;
      
      // 生成路由路径
      const routePath = this.generateRoutePath(filePath);
      
      // 提取支持的 HTTP 方法
      const supportedMethods = this.extractSupportedMethods(module);
      
      if (supportedMethods.length === 0) {
        if (this.options.verbose) {
          console.warn(`⚠️  路由文件未导出任何 HTTP 方法: ${filePath}`);
        }
        return;
      }

      // 使用 app.on 批量注册方法
      this.app.on(supportedMethods, routePath, async (c: Context) => {
        const method = c.req.method.toUpperCase();
        const handler = module[method as keyof RouteHandlers];
        
        if (handler && typeof handler === 'function') {
          try {
            return await handler(c);
          } catch (error) {
            console.error(`❌ 路由处理错误 ${routePath} [${method}]:`, error);
            return c.json({ 
              error: 'Internal Server Error',
              message: process.env.NODE_ENV === 'development' ? 
                (error instanceof Error ? error.message : String(error)) : 
                'Something went wrong'
            }, 500);
          }
        }
        
        return c.json({ error: 'Method not allowed' }, 405);
      });

      // 记录注册的路由
      this.registeredRoutes.push({
        filePath,
        routePath,
        methods: supportedMethods,
        module
      });

      if (this.options.verbose) {
        console.log(`📝 注册文件路由: ${routePath} [${supportedMethods.join(', ')}]`);
      }

    } catch (error) {
      console.error(`❌ 注册路由文件失败 ${filePath}:`, error);
      // 不抛出错误，继续处理其他文件
    }
  }

  /**
   * 从文件路径生成路由路径
   * 支持 Next.js 风格的路由约定
   */
  private generateRoutePath(filePath: string): string {
    // 获取相对路径
    const normalizedRoutesDir = path.resolve(this.options.routesDir);
    const normalizedFilePath = path.resolve(filePath);
    const relativePath = path.relative(normalizedRoutesDir, normalizedFilePath);

    // 统一使用 POSIX 风格的路径分隔符
    let routePath = relativePath
      .replace(/\\/g, '/') // Windows 反斜杠转为正斜杠
      .replace(/\/route\.(ts|js)$/, '') // 移除 /route.ts 或 /route.js
      .replace(/\[\.\.\.([^\]]+)\]/g, '*') // 转换 [...param] 为 * (catch-all) - 必须在普通参数之前
      .replace(/\[([^\]]+)\]/g, ':$1'); // 转换 [param] 为 :param

    // 处理特殊情况
    if (routePath === '' || routePath === '.') {
      routePath = '/';
    } else if (!routePath.startsWith('/')) {
      routePath = '/' + routePath;
    }

    // 添加前缀
    if (this.options.prefix) {
      const prefix = this.options.prefix.startsWith('/') ? this.options.prefix : '/' + this.options.prefix;
      routePath = prefix + routePath;
    }

    return routePath;
  }

  /**
   * 提取模块支持的 HTTP 方法
   */
  private extractSupportedMethods(module: RouteModule): string[] {
    const httpMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS', 'HEAD'];
    const supportedMethods: string[] = [];
    
    for (const method of httpMethods) {
      if (module[method as keyof RouteHandlers] && 
          typeof module[method as keyof RouteHandlers] === 'function') {
        supportedMethods.push(method);
      }
    }
    
    return supportedMethods;
  }

  /**
   * 打印已注册的路由信息
   */
  private printRegisteredRoutes(): void {
    console.log('\n📋 已注册的文件路由:');
    console.log('─'.repeat(60));
    
    for (const route of this.registeredRoutes) {
      const methodsStr = route.methods.map(m => 
        m.padEnd(6)
      ).join(' ');
      console.log(`${methodsStr} ${route.routePath}`);
    }
    
    console.log('─'.repeat(60));
  }

  /**
   * 获取已注册的路由信息
   */
  getRegisteredRoutes(): RegisteredRoute[] {
    return [...this.registeredRoutes];
  }

  /**
   * 清除所有已注册的路由
   */
  clearRoutes(): void {
    this.registeredRoutes = [];
  }
}

/**
 * 创建文件路由器的便捷函数
 */
export async function createFileRouter(
  app: Hono, 
  routesDir: string, 
  options?: Partial<FileRouterOptions>
): Promise<FileBasedRouter> {
  const router = new FileBasedRouter(app, { routesDir, ...options });
  await router.registerFileRoutes();
  return router;
}
