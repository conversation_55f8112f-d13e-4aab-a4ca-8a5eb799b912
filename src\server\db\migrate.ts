import { readFileSync, existsSync } from 'fs';
import { join } from 'path';
import type { DatabaseInstance } from '../types';

/**
 * 通用数据库迁移工具
 * 提供迁移的基础设施，不包含具体业务迁移
 *
 * 注意：具体的业务迁移应该在 src/app/db/migrate.ts 中
 */

export interface MigrationOptions {
  migrationsDir: string;
  client: any; // 原始数据库客户端
}

/**
 * 执行SQL迁移文件
 */
export async function executeSqlMigration(sqlContent: string, client: any) {
  // 分割 SQL 语句（按分号分割）
  const statements = sqlContent
    .split(';')
    .map(stmt => stmt.trim())
    .filter(stmt => stmt.length > 0);

  // 执行每个 SQL 语句
  for (const statement of statements) {
    if (statement.trim()) {
      await client.execute(statement);
      console.log(`✅ Executed: ${statement.substring(0, 50)}...`);
    }
  }
}

/**
 * 通用迁移执行器
 */
export async function runMigrations(options: MigrationOptions) {
  try {
    console.log('🚀 Starting database migrations...');

    const { migrationsDir, client } = options;

    // 检查迁移目录是否存在
    if (!existsSync(migrationsDir)) {
      console.log(`⚠️ Migrations directory not found: ${migrationsDir}`);
      return;
    }

    // 这里可以扩展为读取多个迁移文件
    // 当前简化版本，具体业务迁移在应用层处理
    console.log('✅ Generic migration infrastructure ready');
    console.log('💡 Business-specific migrations should be handled in src/app/db/migrate.ts');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
}

/**
 * 通用表检查工具
 */
export async function checkTables(client: any, tableNames: string[]) {
  try {
    const placeholders = tableNames.map(() => '?').join(', ');
    const result = await client.execute({
      sql: `SELECT name FROM sqlite_master WHERE type='table' AND name IN (${placeholders})`,
      args: tableNames
    });

    const existingTables = result.rows.map(row => row.name);
    const missingTables = tableNames.filter(table => !existingTables.includes(table));

    return {
      allTablesExist: missingTables.length === 0,
      existingTables,
      missingTables
    };
  } catch (error) {
    console.error('Error checking tables:', error);
    return {
      allTablesExist: false,
      existingTables: [],
      missingTables: tableNames
    };
  }
}

/**
 * 通用自动迁移工具
 */
export async function autoMigrate(options: MigrationOptions, requiredTables: string[] = []) {
  if (requiredTables.length === 0) {
    console.log('💡 No tables specified for checking. Use business-specific migration in src/app/db/migrate.ts');
    return;
  }

  const { allTablesExist, missingTables } = await checkTables(options.client, requiredTables);

  if (!allTablesExist) {
    console.log(`🔧 Missing tables detected: ${missingTables.join(', ')}`);
    console.log('🚀 Running automatic migration...');
    await runMigrations(options);
  } else {
    console.log('✅ All required tables exist, skipping migration');
  }
}
