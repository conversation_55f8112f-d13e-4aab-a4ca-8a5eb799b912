{"name": "hono-api", "version": "1.0.0", "description": "A modern API built with Hono, Turso, and Bun", "main": "src/index.ts", "type": "module", "scripts": {"dev": "bun run --watch src/index.ts", "build": "bun build src/index.ts --outdir ./dist --target bun", "start": "bun run dist/index.js", "test": "bun test", "typecheck": "tsc --noEmit"}, "keywords": ["hono", "turso", "bun", "api", "typescript"], "author": "", "license": "ISC", "devDependencies": {"@types/bun": "^1.2.19", "drizzle-kit": "^0.31.4", "typescript": "^5.9.2"}, "dependencies": {"@libsql/client": "^0.15.10", "better-auth": "^1.3.4", "drizzle-orm": "^0.44.4", "glob": "^11.0.3", "hono": "^4.8.12", "quick-lru": "^7.0.1", "uuid": "^11.1.0", "zod": "^4.0.14"}, "engines": {"bun": ">=1.0.0"}}