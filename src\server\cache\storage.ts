import QuickLRU from "quick-lru";

export interface CacheStorage {
  get(key: string): Promise<any>;
  set(key: string, value: any, ttl?: number): Promise<void>;
  delete(key: string): Promise<void>;
  clear(): Promise<void>;
  getStats?(): { size: number; maxSize?: number; remainingSize?: number };
  priority?: number; // 存储优先级，数字越小优先级越高
  backfill?: boolean; // 是否允许回填到此存储
}

export interface StorageOptions {
  maxSize?: number;
  maxAge?: number;
  namespace?: string;
  priority?: number;
  backfill?: boolean;
}

// 基础存储类
export abstract class BaseStorage implements CacheStorage {
  protected namespace: string;
  public priority: number;
  public backfill: boolean;

  constructor(options: StorageOptions = {}) {
    this.namespace = options.namespace || "cache";
    this.priority = options.priority ?? 0;
    this.backfill = options.backfill ?? true;
  }

  protected getNamespacedKey(key: string): string {
    return `${this.namespace}:${key}`;
  }

  abstract get(key: string): Promise<any>;
  abstract set(key: string, value: any, ttl?: number): Promise<void>;
  abstract delete(key: string): Promise<void>;
  abstract clear(): Promise<void>;
  abstract getStats?(): {
    size: number;
    maxSize?: number;
    remainingSize?: number;
  };
}

// 内存存储实现
export class MemoryStorage extends BaseStorage {
  private cache: QuickLRU<string, { value: any; expiry?: number }>;

  constructor(options: StorageOptions = {}) {
    super(options);
    this.cache = new QuickLRU({
      maxSize: options.maxSize || 1000,
      maxAge: options.maxAge || 1000 * 60 * 60,
    });
  }

  async get(key: string): Promise<any> {
    const namespacedKey = this.getNamespacedKey(key);
    const item = this.cache.get(namespacedKey);
    if (!item) return null;

    if (item.expiry && item.expiry < Date.now()) {
      this.cache.delete(namespacedKey);
      return null;
    }

    return item.value;
  }

  async set(key: string, value: any, ttl?: number): Promise<void> {
    const namespacedKey = this.getNamespacedKey(key);
    this.cache.set(namespacedKey, {
      value,
      expiry: ttl ? Date.now() + ttl * 1000 : undefined,
    });
  }

  async delete(key: string): Promise<void> {
    const namespacedKey = this.getNamespacedKey(key);
    this.cache.delete(namespacedKey);
  }

  async clear(): Promise<void> {
    this.cache.clear();
  }

  getStats() {
    return {
      size: this.cache.size,
      maxSize: (this.cache as any).maxSize,
      remainingSize: (this.cache as any).maxSize - this.cache.size,
    };
  }
}

// KV 存储实现
export class KVStorage extends BaseStorage {
  private store: Map<string, { value: any; expiry?: number }>;

  constructor(options: StorageOptions = {}) {
    super(options);
    this.store = new Map();
  }

  async get(key: string): Promise<any> {
    const namespacedKey = this.getNamespacedKey(key);
    const item = this.store.get(namespacedKey);
    if (!item) return null;

    if (item.expiry && item.expiry < Date.now()) {
      this.store.delete(namespacedKey);
      return null;
    }

    return item.value;
  }

  async set(key: string, value: any, ttl?: number): Promise<void> {
    const namespacedKey = this.getNamespacedKey(key);
    this.store.set(namespacedKey, {
      value,
      expiry: ttl ? Date.now() + ttl * 1000 : undefined,
    });
  }

  async delete(key: string): Promise<void> {
    const namespacedKey = this.getNamespacedKey(key);
    this.store.delete(namespacedKey);
  }

  async clear(): Promise<void> {
    this.store.clear();
  }

  getStats() {
    return {
      size: this.store.size,
    };
  }
}

// 创建默认的存储实例
export const memoryStorage = new MemoryStorage({
  maxSize: 1000,
  maxAge: 1000 * 60 * 60 * 24,
  namespace: "memory",
  priority: 1, // 内存存储优先级高
  backfill: true,
});

export const kvStorage = new KVStorage({
  namespace: "kv",
  priority: 2, // KV存储优先级低
  backfill: true,
});
