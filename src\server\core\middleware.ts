import type { Middleware, MiddlewareContext, NextFunction } from "../types";

interface MiddlewareEntry {
  id: string;
  middleware: Middleware;
}

export class MiddlewareManager {
  private middlewares: MiddlewareEntry[] = [];

  use(middleware: Middleware, id: string) {
    if (id) {
      const index = this.middlewares.findIndex((entry) => entry.id === id);
      if (index !== -1) {
        // Replace middleware at original position
        this.middlewares[index] = { id, middleware };
        return this;
      }
    }
    // If no ID or middleware with ID doesn't exist, append to end
    this.middlewares.push({ id, middleware });
    return this;
  }

  async execute(context: Omit<MiddlewareContext, "middlewareManager">) {
    // Inject the manager instance into context
    const contextWithManager = Object.assign(context, {
      middlewareManager: this,
    });

    const executeMiddleware = async (index: number): Promise<void> => {
      if (index >= this.middlewares.length) {
        return;
      }

      const { middleware } = this.middlewares[index];
      const next: NextFunction = async () => {
        await executeMiddleware(index + 1);
      };

      await middleware(contextWithManager, next);
    };
    await executeMiddleware(0);
  }

  getMiddlewares(): MiddlewareEntry[] {
    return this.middlewares
  }

  hasMiddleware(id: string) {
    return this.middlewares.find((entry) => entry.id === id);
  }
}

export const createMiddlewareManager = () => new MiddlewareManager();
