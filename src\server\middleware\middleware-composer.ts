// 中间件组合器 - 用于将积分功能集成到现有路由构建器
import type { Middleware } from "../types";
import { createCreditMiddleware, type CreditMiddlewareConfig } from "./credits";
import { createRateLimitMiddleware, type RateLimitConfig } from "./rate-limit";
import { createAuthMiddleware, type AuthMiddlewareConfig } from "./auth";

/**
 * 中间件配置接口
 */
export interface MiddlewareConfig {
  auth?: AuthMiddlewareConfig;
  credits?: CreditMiddlewareConfig;
  rateLimit?: RateLimitConfig;
  custom?: Middleware[];
}

/**
 * 中间件组合器类
 */
export class MiddlewareComposer {
  private middlewares: Middleware[] = [];

  /**
   * 添加认证中间件
   */
  withAuth(config: AuthMiddlewareConfig): this {
    this.middlewares.push(createAuthMiddleware(config));
    return this;
  }

  /**
   * 添加积分中间件
   */
  withCredits(config: CreditMiddlewareConfig): this {
    this.middlewares.push(createCreditMiddleware(config));
    return this;
  }

  /**
   * 添加率限制中间件
   */
  withRateLimit(config: RateLimitConfig): this {
    this.middlewares.push(createRateLimitMiddleware(config));
    return this;
  }

  /**
   * 添加自定义中间件
   */
  withCustom(middleware: Middleware): this {
    this.middlewares.push(middleware);
    return this;
  }

  /**
   * 批量添加中间件
   */
  withMiddlewares(middlewares: Middleware[]): this {
    this.middlewares.push(...middlewares);
    return this;
  }

  /**
   * 从配置对象创建中间件
   */
  fromConfig(config: MiddlewareConfig): this {
    if (config.auth) {
      this.withAuth(config.auth);
    }
    if (config.credits) {
      this.withCredits(config.credits);
    }
    if (config.rateLimit) {
      this.withRateLimit(config.rateLimit);
    }
    if (config.custom) {
      this.withMiddlewares(config.custom);
    }
    return this;
  }

  /**
   * 构建中间件数组
   */
  build(): Middleware[] {
    return [...this.middlewares];
  }

  /**
   * 清空中间件
   */
  clear(): this {
    this.middlewares = [];
    return this;
  }
}

/**
 * 中间件集成扩展 - 为现有路由构建器添加中间件支持
 */
export interface MiddlewareIntegration {
  /**
   * 应用中间件配置
   */
  withMiddleware(config: MiddlewareConfig): this;
  
  /**
   * 应用积分配置
   */
  withCredits(config: CreditMiddlewareConfig): this;

  /**
   * 应用认证配置
   */
  withAuth(config: AuthMiddlewareConfig): this;
  
  /**
   * 应用率限制配置
   */
  withRateLimit(config: RateLimitConfig): this;
}

/**
 * 为现有路由构建器添加中间件支持的 Mixin
 */
export function withMiddlewareSupport<T extends { middlewareManager: any }>(Base: new (...args: any[]) => T) {
  return class extends Base implements MiddlewareIntegration {
    private middlewareComposer = new MiddlewareComposer();

    /**
     * 应用中间件配置
     */
    withMiddleware(config: MiddlewareConfig): this {
      const middlewares = this.middlewareComposer.fromConfig(config).build();
      middlewares.forEach(middleware => {
        this.middlewareManager.add(`middleware_${Date.now()}_${Math.random()}`, middleware);
      });
      this.middlewareComposer.clear();
      return this;
    }

    /**
     * 应用积分配置
     */
    withCredits(config: CreditMiddlewareConfig): this {
      const creditMiddleware = createCreditMiddleware(config);
      this.middlewareManager.add('credits', creditMiddleware);
      return this;
    }

    /**
     * 应用认证配置
     */
    withAuth(config: AuthMiddlewareConfig): this {
      const authMiddleware = createAuthMiddleware(config);
      this.middlewareManager.add('auth', authMiddleware);
      return this;
    }

    /**
     * 应用率限制配置
     */
    withRateLimit(config: RateLimitConfig): this {
      const rateLimitMiddleware = createRateLimitMiddleware(config);
      this.middlewareManager.add('rateLimit', rateLimitMiddleware);
      return this;
    }
  };
}

/**
 * 快捷函数：创建标准的中间件配置
 */
export function createStandardMiddleware(options: {
  requireAuth?: boolean;
  creditConfig?: CreditMiddlewareConfig;
  rateLimitConfig?: RateLimitConfig;
}): MiddlewareConfig {
  const config: MiddlewareConfig = {};

  if (options.requireAuth) {
    config.auth = { required: true };
  }

  if (options.creditConfig) {
    config.credits = options.creditConfig;
  }

  if (options.rateLimitConfig) {
    config.rateLimit = options.rateLimitConfig;
  }

  return config;
}

/**
 * 快捷函数：创建 API 端点的标准配置
 */
export function createApiMiddleware(options: {
  auth?: boolean;
  credits?: {
    read?: number;
    write?: number;
    delete?: number;
  };
  rateLimit?: {
    window?: number;
    max?: number;
  };
} = {}): MiddlewareConfig {
  return new MiddlewareComposer()
    .fromConfig({
      auth: options.auth ? { required: true } : undefined,
      credits: options.credits ? {
        methods: {
          GET: { cost: options.credits.read || 1, deductionStrategy: 'post' },
          POST: { cost: options.credits.write || 5, deductionStrategy: 'post', refundOnFailure: true },
          PUT: { cost: options.credits.write || 5, deductionStrategy: 'post', refundOnFailure: true },
          DELETE: { cost: options.credits.delete || 10, deductionStrategy: 'pre', refundOnFailure: true },
        }
      } : undefined,
      rateLimit: options.rateLimit ? {
        window: options.rateLimit.window || 60,
        max: options.rateLimit.max || 100,
      } : undefined,
    })
    .build()
    .reduce((config, middleware, index) => {
      if (!config.custom) config.custom = [];
      config.custom.push(middleware);
      return config;
    }, {} as MiddlewareConfig);
}

// Rate limit configuration interface
interface RateLimitConfig {
  window?: number;
  max?: number;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}
